// eslint-disable-next-line import/no-unresolved
import { MemberMetafield } from "@/types/memberTypes";

export async function updateCustomerTierMetafield(admin: any, customerId: string, newTier: string) {
  if (!newTier) return;

  try {
    await admin.graphql(
      `
      mutation updateCustomerMetafield($input: CustomerInput!) {
        customerUpdate(input: $input) {
          customer {
            id
          }
          userErrors {
            field
            message
          }
        }
      }
    `,
      {
        variables: {
          input: {
            id: customerId,
            metafields: [
              {
                key: "vip_tier",
                value: newTier,
                type: "single_line_text_field",
              },
            ],
          },
        },
      },
    );
  } catch (error) {
    console.error("Error updating customer tier metafield:", error);
  }
}

export async function updateCustomerPointMetafield(
  admin: any,
  customerId: string,
  newPoint: string,
) {
  if (!newPoint) return;

  try {
    await admin.graphql(
      `
      mutation updateCustomerMetafield($input: CustomerInput!) {
        customerUpdate(input: $input) {
          customer {
            id
          }
          userErrors {
            field
            message
          }
        }
      }
    `,
      {
        variables: {
          input: {
            id: customerId,
            metafields: [
              {
                key: "points",
                value: newPoint,
                type: "single_line_text_field",
              },
            ],
          },
        },
      },
    );
  } catch (error) {
    console.error("Error updating customer point metafield:", error);
  }
}

export async function updateOrderPointMetafield(admin: any, orderId: string, newPoint: string) {
  if (!newPoint) return;

  try {
    await admin.graphql(
      `
      mutation updateOrderMetafield($input: OrderInput!){
        orderUpdate(input: $input) {
          order {
            id
          }
          userErrors {
            field
            message
          }
        }
      }`,
      {
        variables: {
          input: {
            id: orderId,
            metafields: [
              {
                key: "redeemPoints",
                value: newPoint,
                type: "single_line_text_field",
              },
            ],
          },
        },
      },
    );
  } catch (error) {
    console.error("Error updating order point metafield:", error);
  }
}

export async function updateCustomerCompleteMetafield(
  admin: any,
  customerId: string,
  newPoint: string,
  namespace: string,
) {
  if (!newPoint) return;

  try {
    await admin.graphql(
      `
      mutation updateCustomerMetafield($input: CustomerInput!) {
        customerUpdate(input: $input) {
          customer {
            id
          }
          userErrors {
            field
            message
          }
        }
      }
    `,
      {
        variables: {
          input: {
            id: customerId,
            metafields: [
              {
                namespace: namespace,
                key: "points",
                value: newPoint,
                type: "single_line_text_field",
              },
              {
                namespace: namespace,
                key: "isCompletedProfile",
                value: "true",
                type: "boolean",
              },
            ],
          },
        },
      },
    );
  } catch (error) {
    console.error("Error updating customer complete metafield:", error);
  }
}

export async function getNamespaceMetafield(admin: any) {
  if (!admin) return;
  const appResponse = await admin.graphql(
    `
    #graphql
    query getApp {
      app {
        id
      }
    }
  `,
  );

  const appResponseJson = await appResponse.json();

  const appId = appResponseJson.data?.app?.id?.replace("gid://shopify/App/", "");

  const namespace = `app--${appId}`;

  return namespace;
}

export async function getReturn(admin: any, returnId: string) {
  if (!returnId) return;
  const response = await admin.graphql(
    `query getReturn($id: ID!){
      return(id: $id) {
        id
        refunds(first: 1) {
          edges {
            node {
              totalRefundedSet {
                shopMoney {
                  amount
                }
              }
            }
          }
        }
        order {
          id
          refunds {
            id
          }
          totalPriceSet{
            shopMoney {
              amount
            }
          }
          metafields(first: 10) {
            edges {
              node {
                namespace
                key
                value
              }
            }
          }
          customer {
            id
            metafields(first: 10) {
              edges {
                node {
                  namespace
                  key
                  value
                }
              }
            }
          }
        }
      }
    }`,
    { variables: { id: returnId } },
  );
  const responseJson = await response.json();
  const returnData = responseJson?.data?.return;

  return returnData;
}

export async function getMetafields(admin: any, edges: any) {
  if (!edges) return;
  const namespace = (await getNamespaceMetafield(admin)) as string;
  return edges
    .filter((m: any) => m.node.namespace === namespace)
    .map((m: any) => ({
      namespace: m.node.namespace,
      key: m.node.key,
      value: m.node.value,
    })) as MemberMetafield[];
}
