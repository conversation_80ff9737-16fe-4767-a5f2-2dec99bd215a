import { BlockStack, Button, InlineStack, Text } from "@shopify/polaris";
import React from "react";
import { useTranslation } from "react-i18next";

export interface CardHearderOnAddProps {
  /** Header Text */
  headerText: string;
  /** Subheader Text */
  subHeaderText: string;
  /** callback when click Add New */
  onAdd: () => void;
}

const CardHearderOnAdd: React.FC<CardHearderOnAddProps> = ({
  headerText,
  subHeaderText,
  onAdd,
}) => {
  const { t } = useTranslation();

  return (
    <div style={{ marginBottom: "16px" }}>
      <InlineStack blockAlign="center" align="space-between" gap="200" wrap={false}>
        <BlockStack gap={"200"}>
          <Text as="h1" variant="headingMd" fontWeight="bold">
            {headerText}
          </Text>
          <Text as="p" variant="bodySm" tone="subdued">
            {subHeaderText}
          </Text>
        </BlockStack>

        <Button variant="secondary" onClick={onAdd}>
          + {t("common.add")}
        </Button>
      </InlineStack>
    </div>
  );
};

export default CardHearderOnAdd;
