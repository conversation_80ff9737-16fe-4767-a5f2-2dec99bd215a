import db from "@/db.server";
import { gql } from "@apollo/client/core";
import { type LoaderFunctionArgs } from "@remix-run/node";
import { authenticate } from "../shopify.server";
import { clientByShop } from "../utils/auth.server";

const GET_CUSTOMER_DISCOUNTS = gql`
  query GetCustomerDiscounts($customerGid: ID!, $first: Int!, $after: String) {
    discountCodes(
      first: $first
      after: $after
      query: "customerSelection.customerIds:$customerGid"
    ) {
      edges {
        cursor
        node {
          id
          code
          customerSelection {
            __typename
            ... on DiscountCustomers {
              customerIds
            }
            ... on DiscountCustomerSegments {
              customerSegmentIds
            }
            ... on DiscountCustomerAll {
              allCustomers
            }
          }
        }
      }
      pageInfo {
        hasNextPage
        endCursor
      }
    }
  }
`;

export const loader = async ({ request }: LoaderFunctionArgs) => {
  // 1. Authenticate the request via Shopify Customer Account (returns sessionToken with sub = customerId)
  const { cors, sessionToken } = await authenticate.public.customerAccount(request);
  const { dest: myshopifyDomain, sub: numericCustomerId } = sessionToken;

  // 2. Look up the shop in your own DB so you can create an Apollo client
  const shop = await db.shop.findFirstOrThrow({
    where: { myshopifyDomain },
  });

  const gqlClient = await clientByShop(shop);

  // 3. Fetch all discount codes assigned to this customer
  async function fetchAllDiscountsForCustomer(customerId: string) {
    // Convert to global ID format
    const customerGid = `gid://shopify/Customer/${customerId}`;
    const perPage = 50;
    let hasNextPage = true;
    let afterCursor: string | null = null;
    const allDiscounts: Array<{
      id: string;
      code: string;
      customerSelection:
        | { __typename: "DiscountCustomers"; customerIds: string[] }
        | { __typename: "DiscountCustomerSegments"; customerSegmentIds: string[] }
        | { __typename: "DiscountCustomerAll" };
    }> = [];

    while (hasNextPage) {
      const { data } = await gqlClient.query<{
        discountCodes: {
          edges: Array<{
            cursor: string;
            node: {
              id: string;
              code: string;
              customerSelection: {
                __typename: string;
                customerIds?: string[];
                customerSegmentIds?: string[];
              };
            };
          }>;
          pageInfo: { hasNextPage: boolean; endCursor: string | null };
        };
      }>({
        query: GET_CUSTOMER_DISCOUNTS,
        variables: {
          customerGid,
          first: perPage,
          after: afterCursor ?? undefined,
        },
        fetchPolicy: "network-only",
      });

      data.discountCodes.edges.forEach(({ node }) => {
        // Narrow the union for TypeScript
        if (node.customerSelection.__typename === "DiscountCustomers") {
          allDiscounts.push({
            id: node.id,
            code: node.code,
            customerSelection: {
              __typename: "DiscountCustomers",
              customerIds: node.customerSelection.customerIds ?? [],
            },
          });
        } else if (node.customerSelection.__typename === "DiscountCustomerSegments") {
          allDiscounts.push({
            id: node.id,
            code: node.code,
            customerSelection: {
              __typename: "DiscountCustomerSegments",
              customerSegmentIds: node.customerSelection.customerSegmentIds ?? [],
            },
          });
        } else {
          // DiscountCustomerAll
          allDiscounts.push({
            id: node.id,
            code: node.code,
            customerSelection: { __typename: "DiscountCustomerAll" },
          });
        }
      });

      hasNextPage = data.discountCodes.pageInfo.hasNextPage;
      afterCursor = data.discountCodes.pageInfo.endCursor;
    }

    return allDiscounts;
  }

  // 4. Actually call the function with the real customer ID
  let discounts = [];
  try {
    discounts = await fetchAllDiscountsForCustomer(numericCustomerId);
  } catch (err) {
    console.error("Error fetching customer discounts:", err);
    // You can choose to return an empty array or bubble up an error
    return cors(
      Response.json({ error: "Could not fetch discounts for this customer." }, { status: 500 }),
    );
  }

  // 5. Return the list of discounts in JSON
  return cors(
    Response.json(
      {
        shop: myshopifyDomain,
        customerId: numericCustomerId,
        discounts,
      },
      { status: 200 },
    ),
  );
};

export const action = async ({ request }: LoaderFunctionArgs) => {
  const { cors, sessionToken } = await authenticate.public.customerAccount(request);
  console.log("sessionToken", sessionToken);
  return cors(Response.json({ message: "Action endpoint reached" }));
};
