import { LoaderFunctionArgs } from "@remix-run/node";
import db from "../../db.server";
import { authenticate } from "../../shopify.server";
import { LoyaltyProgramTypeMapInDatabaseType, UrlLoyaltyType } from "./interface";

/**
 * Loader function for the loyalty program page
 */
export async function loader({ request }: LoaderFunctionArgs) {
  const { session } = await authenticate.admin(request);
  const url = new URL(request.url);
  const parts = url.pathname.split("/");
  const loyaltyProgramType = parts[parts.length - 1];

  const shop = await db.shop.findFirst({
    where: {
      myshopifyDomain: session.shop,
    },
  });

  if (!shop) {
    // Using a standardized error format that can be translated on the client side
    throw new Error(`SHOP_NOT_FOUND:${session.shop}`);
  }

  const loyaltyProgramTypeInDb =
    LoyaltyProgramTypeMapInDatabaseType[loyaltyProgramType.toLowerCase() as UrlLoyaltyType];

  const loyaltyProgram = await db.loyaltyProgram.findFirst({
    where: {
      programType: loyaltyProgramTypeInDb,
      shopId: shop.id,
    },
  });

  return Response.json({ loyaltyProgram, loyaltyProgramTypeInDb });
}

/**
 * Type for the loader data
 */
export type LoaderData = {
  loyaltyProgram: any; // Replace with proper type if available
  loyaltyProgramTypeInDb: string;
};
