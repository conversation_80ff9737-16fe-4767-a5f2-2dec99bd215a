import { BlockStack, Box, Button, Icon, InlineStack, Modal, Text } from "@shopify/polaris";
import { CreditCardIcon, DeliveryIcon, DiscountIcon, MoneyIcon } from "@shopify/polaris-icons";
import React from "react";

// Define the props for the AddRewardModal component
interface AddRewardModalProps {
  open: boolean; // Controls whether the modal is open or closed
  onClose: () => void; // Function to call when the modal should be closed
  onAddReward: (type: "points" | "storeCredit" | "amountOffOrder" | "freeShipping") => void; // Function to call when a reward type is selected
}

/**
 * Modal component to select the type of new reward to add.
 * @param {AddRewardModalProps} props - The component's props.
 * @returns {React.ReactElement} The AddRewardModal component.
 */
export const AddRewardModal: React.FC<AddRewardModalProps> = ({ open, onClose, onAddReward }) => {
  // Handler function when an 'Add' button is clicked for a specific reward type
  const handleAddType = (type: "points" | "storeCredit" | "amountOffOrder" | "freeShipping") => {
    console.log(`Selected reward type to add: ${type}`);
    onAddReward(type); // Call the prop function with the selected type
    // onClose(); // Decide whether to close modal immediately or let the parent handle navigation
  };

  return (
    <Modal
      open={open}
      onClose={onClose}
      title="Add new reward"
      primaryAction={{
        content: "Cancel",
        onAction: onClose,
      }}
    >
      {/* Use Modal.Section as shown in the provided documentation example */}
      <Modal.Section>
        <BlockStack gap="500">
          {/* Points Reward Option */}
          <Box paddingBlockEnd="200">
            <InlineStack align="space-between">
              <InlineStack gap="300">
                <Icon source={MoneyIcon} />
                <Text variant="bodyMd" as={"p"}>
                  Points
                </Text>
              </InlineStack>
              <Button onClick={() => handleAddType("points")}>Add</Button>
            </InlineStack>
          </Box>

          {/* Store Credit Reward Option */}
          <Box paddingBlockEnd="200">
            <InlineStack align="space-between">
              <InlineStack gap="300">
                <Icon source={CreditCardIcon} />
                <Text variant="bodyMd" as={"p"}>
                  Store credit
                </Text>
              </InlineStack>
              <Button onClick={() => handleAddType("storeCredit")}>Add</Button>
            </InlineStack>
          </Box>

          {/* Amount off order Reward Option */}
          <Box paddingBlockEnd="200">
            <InlineStack align="space-between">
              <InlineStack gap="300">
                <Icon source={DiscountIcon} />
                <Text variant="bodyMd" as={"p"}>
                  Amount off order
                </Text>
              </InlineStack>
              <Button onClick={() => handleAddType("amountOffOrder")}>Add</Button>
            </InlineStack>
          </Box>

          {/* Free shipping discount Reward Option */}
          <Box paddingBlockEnd="200">
            <InlineStack align="space-between">
              <InlineStack gap="300">
                <Icon source={DeliveryIcon} />
                <Text variant="bodyMd" as={"p"}>
                  Free shipping discount
                </Text>
              </InlineStack>
              <Button onClick={() => handleAddType("freeShipping")}>Add</Button>
            </InlineStack>
          </Box>
        </BlockStack>
      </Modal.Section>
    </Modal>
  );
};
