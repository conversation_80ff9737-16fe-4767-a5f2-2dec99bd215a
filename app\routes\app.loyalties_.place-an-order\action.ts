import {DiscountType, RequirementType, RewardType, WaysEarnRewardType} from "@prisma/client";
import type {ActionFunctionArgs} from "@remix-run/node";
import db from "../../db.server";
import {authenticate} from "../../shopify.server";

/**
 * Interface for frontend reward data
 */
interface RewardData {
  id?: string;
  type: string;
  title: string;
  value: string;
  discountType?: string | null;
  minimumRequirement?: string | null;
  minimumValue?: string | null;
  productDiscounts?: boolean;
  orderDiscounts?: boolean;
  shippingDiscounts?: boolean;
}

/**
 * Interface for frontend vip data
 */
interface LoyaltyVIPTier {
  id: number;
  createdAt: Date;
  updatedAt: Date;
  loyaltyProgramId: number;
  name: string;
  spendRequirement: number | null;
  pointsRequirement: number | null;
  spendAmount: number | null;
  pointEarn: number | null;
  basedOnDiffTier: boolean;
}

/**
 * Helper function to map frontend reward type to Prisma enum
 */
function mapRewardType(type: string): RewardType {
  switch (type) {
    case "points":
      return RewardType.POINTS;
    case "store-credit":
      return RewardType.STORE_CREDIT;
    default:
      return RewardType.POINTS;
  }
}

/**
 * Helper function to map frontend discount type to Prisma enum
 */
function mapDiscountType(type: string | undefined | null): DiscountType | undefined {
  if (!type) return undefined;
  return type === "percentage" ? DiscountType.PERCENTAGE : DiscountType.FIXED;
}

/**
 * Helper function to map frontend requirement type to Prisma enum
 */
function mapRequirementType(type: string | undefined | null): RequirementType | undefined {
  if (!type) return undefined;
  switch (type) {
    case "none":
      return RequirementType.NONE;
    case "amount":
      return RequirementType.AMOUNT;
    case "quantity":
      return RequirementType.QUANTITY;
    default:
      return undefined;
  }
}

/**
 * Process rewards for a WaysEarnReward
 */
async function processRewards(waysEarnRewardId: number, rewards: RewardData[]) {
  // First, delete all existing rewards for this WaysEarnReward
  try {
    await db.placeAnOrderReward.deleteMany({
      where: {
        waysEarnRewardId,
      },
    });
  } catch (error) {
    console.error("Error deleting existing rewards:", error);
  }

  // Process each reward
  for (const reward of rewards) {
    try {
      // Create a new reward using Prisma's type-safe API
      await db.placeAnOrderReward.create({
        data: {
          waysEarnRewardId,
          title: reward.title,
          rewardType: mapRewardType(reward.type),
          value: reward.value,
          discountType: reward.discountType ? mapDiscountType(reward.discountType) : undefined,
          minimumRequirement: reward.minimumRequirement
            ? mapRequirementType(reward.minimumRequirement)
            : undefined,
          minimumValue: reward.minimumValue
            ? parseFloat(reward.minimumValue.toString())
            : undefined,
          productDiscounts: reward.productDiscounts ?? false,
          orderDiscounts: reward.orderDiscounts ?? false,
          shippingDiscounts: reward.shippingDiscounts ?? false,
        },
      });
    } catch (error) {
      console.error("Error creating reward:", error);
    }
  }
}


/**
 * Process rewards for a loyaltyVipTier
 */
async function processVipTiers(vipTiers: LoyaltyVIPTier[]) {
  console.log("Processing VIP Tiers:", vipTiers);
  // Process each reward
  for (const tier of vipTiers) {
    try {
      // Create a new reward using Prisma's type-safe API
      await db.loyaltyVIPTier.update({
        where: {id: tier.id},
        data: {
          spendAmount: tier.spendAmount ? parseFloat(tier.spendAmount.toString()) : null,
          pointEarn: tier.pointEarn ? parseFloat(tier.pointEarn.toString()) : null,
          basedOnDiffTier: tier.basedOnDiffTier
        },
      });
    } catch (error) {
      console.error("Error Update VipTiers:", error);
    }
  }
}

/**
 * Action function for the place an order program page
 */
export async function action({request}: ActionFunctionArgs) {
  const {session} = await authenticate.admin(request);
  const shop = await db.shop.findFirst({where: {myshopifyDomain: session.shop}});
  if (!shop) {
    throw new Error("Shop not found");
  }
  const data = await request.json();
  const {title, isActive, rewards, vipTiers} = data;

  // Find or create the WaysEarnReward for place an order
  let waysEarnReward = await db.waysEarnReward.findFirst({
    where: {
      shopId: shop.id,
      typeEarnReward: WaysEarnRewardType.PURCHASE,
    },
  });

  if (waysEarnReward) {
    // Update the existing WaysEarnReward
    waysEarnReward = await db.waysEarnReward.update({
      where: {id: waysEarnReward.id},
      data: {
        title,
        isActive,
      },
    });
  } else {
    // Create a new WaysEarnReward
    waysEarnReward = await db.waysEarnReward.create({
      data: {
        shopId: shop.id,
        typeEarnReward: WaysEarnRewardType.PURCHASE,
        title,
        isActive,
      },
    });
  }

  // Process rewards if they are provided
  if (rewards && Array.isArray(rewards)) {
    // Use the extracted processRewards function
    await processRewards(waysEarnReward.id, rewards);
  }

  if (vipTiers && Array.isArray(vipTiers)) {
    // Use the extracted processVipTiers function
    await processVipTiers(vipTiers);
  }

  return {success: true};
}
