import { LoaderFunctionArgs } from "@remix-run/node";
import { Layout, Page } from "@shopify/polaris";
import { authenticate } from "../../shopify.server";
import FeaturesSection from "./components/FeaturesSection";
import HelpSection from "./components/HelpSection";
import LanguageSelector from "./components/LanguageSelector";
import WelcomeSection from "./components/WelcomeSection";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  await authenticate.admin(request);

  return {};
};

export default function WelcomePage() {
  return (
    <Page>
      <Layout>
        <Layout.Section>
          <LanguageSelector />
        </Layout.Section>

        <Layout.Section>
          <WelcomeSection />
        </Layout.Section>

        <Layout.Section>
          <FeaturesSection />
        </Layout.Section>

        <Layout.Section>
          <div className="border-t border-gray-200"></div>
        </Layout.Section>

        <Layout.Section>
          <HelpSection />
        </Layout.Section>

        <Layout.Section></Layout.Section>
      </Layout>
    </Page>
  );
}
