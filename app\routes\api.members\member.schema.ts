import dayjs from "dayjs";
import customParseFormat from "dayjs/plugin/customParseFormat";
import Jo<PERSON> from "joi";
dayjs.extend(customParseFormat);

// Define the Gender type
export type Gender = "Male" | "Female" | "Other";

// Define the interface for member data
export interface IMember {
  // Required fields
  readonly cellphone: string;
  readonly memberId: string;
  readonly registerDate: string;
  readonly registerLocation: string;
  readonly registerEmployee: string;

  // Optional fields
  readonly email?: string;
  readonly actionDate?: string;
  readonly lastName?: string;
  readonly firstName?: string;
  readonly birthday?: string;
  readonly gender?: Gender;
  readonly address?: string;
  readonly marketingNotify?: boolean;
}

export const phoneSchema = Joi.string()
  .required()
  .trim()
  .regex(/^09\d{8}$/)
  .messages({
    "any.required": "The 'cellphone' is required",
    "string.empty": "The 'cellphone' cannot be empty",
    "string.pattern.base":
      "The 'cellphone' field has an invalid cellphone format. The total numbers must be 10 digits and start with '09'.",
  });

export const memberSchema = Joi.object<IMember>({
  cellphone: phoneSchema,
  registerDate: Joi.date().iso().required().messages({
    "any.required": "The 'registerDate' is required",
    "date.format":
      "The 'registerDate' field has an invalid date/time format. Please use YYYY-MM-DDTHH:mm:ss format",
  }),
  registerLocation: Joi.string().max(255).required().messages({
    "any.required": "The 'registerLocation' is required",
    "string.max": "The 'registerLocation' cannot exceed 255 characters",
  }),
  registerEmployee: Joi.string().max(255).required().messages({
    "any.required": "The 'registerEmployee' is required",
    "string.max": "The 'registerEmployee' cannot exceed 255 characters",
  }),
  email: Joi.string().max(255).email().allow("").optional().messages({
    "string.email": "The 'email' format is invalid",
    "string.max": "The 'email' cannot exceed 255 characters",
  }),
  lastName: Joi.string().max(255).allow("").optional().messages({
    "string.max": "The 'lastName' cannot exceed 255 characters",
  }),
  firstName: Joi.string().max(255).allow("").optional().messages({
    "string.max": "The 'firstName' cannot exceed 255 characters",
  }),
  birthday: Joi.string()
    .custom((value, helpers) => {
      const isValid = dayjs(value, "YYYY-MM-DD", true).isValid();

      if (!isValid) {
        return helpers.error("date.format");
      }
    })
    .allow("")
    .optional()
    .messages({
      "date.format": "The 'birthday' has an invalid date format. Please use YYYY-MM-DD format",
    }),
  gender: Joi.string().valid("Male", "Female", "Other").allow("").optional().messages({
    "any.only": "The 'gender' must be one of: Male, Female, Other",
  }),
  address: Joi.string().max(255).allow("").optional().messages({
    "string.max": "The 'address' cannot exceed 255 characters",
  }),
  marketingNotify: Joi.boolean().optional().messages({
    "boolean.base": "The 'marketingNotify' must be true or false",
  }),
});

export const memberShopifyInputToMemberMap = {
  phone: "cellphone",
  firstName: "firstName",
  lastName: "lastName",
  email: "email",
  metafields: [
    {
      key: "birthday",
      type: "date",
    },
    {
      key: "gender",
      type: "string",
    },
  ],
  addresses: [
    {
      key: "address",
      type: "string",
    },
  ],
  emailMarketingConsent: [
    {
      key: "marketingNotify",
      type: "boolean",
    },
  ],
};

type UserError = {
  field: string[];
  message: string;
};

type MappedError = {
  field: string;
  code: string;
  message: string;
};

export const mapErrors = (errors: UserError[]): MappedError[] => {
  const codeMapping: { [key: string]: string } = {
    "Email has already been taken": "duplicate_value",
    "Phone has already been taken": "duplicate_value",
    "Value must be in YYYY-MM-DD format": "invalid_format",
  };

  const fieldMapping: { [key: string]: string } = {
    email: "email",
    phone: "cellphone",
  };

  const messageMapping: { [key: string]: string } = {
    "Email has already been taken": "Email has already been taken",
    "Phone has already been taken": "A member with this 'cellphone' already exists.",
  };

  return errors.map((error) => {
    return {
      field: fieldMapping[error.field[0]] || error.field[0],
      code: codeMapping[error.message] || "invalid_value",
      message: messageMapping[error.message] || error.message,
    };
  });
};

type ShopifyCustomer = {
  id: string;
  email: string;
  phone: string;
  taxExempt: boolean;
  emailMarketingConsent: {
    marketingState: string;
    marketingOptInLevel: string;
    consentUpdatedAt: string | null;
  };
  firstName: string;
  lastName: string;
  smsMarketingConsent: {
    marketingState: string;
    marketingOptInLevel: string;
  };
  addresses: { address1: string }[];
  metafields: {
    nodes: {
      id: string;
      key: string;
      namespace: string;
      value: string;
    }[];
  };
};

type TransformedCustomer = {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  cellphone: string;
  birthday: string | null;
  gender: string | null;
  registerDate: string | null;
  registerEmployee: string | null;
  registerLocation: string | null;
  address: string | null;
  marketingNotify: boolean | null;
};

// Function to transform the Shopify customer data
export const transformCustomerData = (customer: ShopifyCustomer): TransformedCustomer => {
  // Extract metafields into a key-value object for easy access
  const metafieldsMap: { [key: string]: string | null } = customer?.metafields?.nodes?.reduce(
    (acc, field) => {
      acc[field.key] = field.value;
      return acc;
    },
    {} as { [key: string]: string | null },
  );

  return {
    id: customer.id.split("/").pop() ?? "",
    firstName: customer.firstName,
    lastName: customer.lastName,
    email: customer.email,
    cellphone: customer.phone,
    birthday: metafieldsMap?.["birth_date"] ?? null,
    gender: metafieldsMap?.["gender"] ?? null,
    registerDate: metafieldsMap?.["register_date"] ?? null,
    registerEmployee: metafieldsMap?.["register_employee"] ?? null,
    registerLocation: metafieldsMap?.["register_location"] ?? null,
    address: customer.addresses?.length > 0 ? customer.addresses[0].address1 : null,
    marketingNotify: customer.emailMarketingConsent?.marketingState === "SUBSCRIBED",
  };
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const mapJoiErrorToMemberError = (errors: any[]): MappedError[] => {
  const codeMapping: { [key: string]: string } = {
    "any.required": "missing_field",
    "string.empty": "missing_field",
    "string.pattern.base": "invalid_format",
    "date.format": "invalid_format",
    "string.email": "invalid_format",
  };

  return errors.map((error) => {
    const fieldMapping: { [key: string]: string } = {
      registerDate: "registerDate",
    };

    return {
      field: fieldMapping[error.path[0]] || error.path[0],
      code: codeMapping[error.type] || "invalid_value",
      message: error.message,
    };
  });
};

export const mapToShopifyInput = (data: {
  cellphone?: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  birthday?: string;
  gender?: string;
  registerDate?: string;
  registerLocation?: string;
  registerEmployee?: string;
  address?: string;
  marketingNotify?: boolean;
}): {
  phone?: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  metafields?: Record<string, string>[];
  addresses?: {
    address1: string;
  }[];
  emailMarketingConsent?: {
    marketingOptInLevel: "CONFIRMED_OPT_IN" | "UNKNOWN";
    marketingState: "UNSUBSCRIBED" | "SUBSCRIBED";
  };
} => {
  return {
    phone: data.cellphone ? `${data.cellphone}` : undefined,
    firstName: data.firstName,
    lastName: data.lastName,
    email: data.email,
    metafields: [
      ...(data.birthday
        ? [
            {
              key: "birth_date",
              value: data.birthday,
              type: "date",
            },
          ]
        : []),
      ...(data.gender
        ? [
            {
              key: "gender",
              value: data.gender,
              type: "single_line_text_field",
            },
          ]
        : []),
      ...(data.registerDate
        ? [
            {
              key: "register_date",
              value: data.registerDate,
              type: "date_time",
            },
          ]
        : []),
      ...(data.registerEmployee
        ? [
            {
              key: "register_employee",
              value: data.registerEmployee,
              type: "single_line_text_field",
            },
          ]
        : []),
      ...(data.registerLocation
        ? [
            {
              key: "register_location",
              value: data.registerLocation,
              type: "single_line_text_field",
            },
          ]
        : []),
    ],
    addresses: [
      ...(data.address
        ? [
            {
              address1: data.address,
            },
          ]
        : []),
    ],
    emailMarketingConsent: data.marketingNotify
      ? {
          marketingOptInLevel: "UNKNOWN",
          marketingState: "SUBSCRIBED",
        }
      : undefined,
  };
};
