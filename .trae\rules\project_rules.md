# File: .trae/rules/project_rules.md

# (Optional front-matter if supported in future; for now treat as plain MD)

# description: Project-wide AI guidelines for our Node.js/Remix Shopify app

# globs:

# - "routes/\*_/_.tsx"

# - "app/components/\*_/_.ts"

# alwaysApply: true

## Project-Wide Coding Standards

- **Use 2-space indentation** in all JavaScript/TypeScript files.
- **Enforce JSDoc** on every exported function: include `@param` and `@returns`.
- **Prefer React hooks** over class components in `app/components/`.

## Remix-Specific Patterns

- In `routes/*.tsx`, always use `export const loader` for data fetching.
- Use `defer()` for non-critical data to improve initial load performance.

## Shopify API Conventions

- Validate webhook signatures in `webhooks/*.ts` using `verifyWebhook()` before processing.

<!-- Next time you add a new pattern or template, append below: -->
