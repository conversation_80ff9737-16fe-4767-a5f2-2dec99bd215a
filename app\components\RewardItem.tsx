import { Button, Icon, Text, BlockStack, InlineStack } from "@shopify/polaris";
import {
  EditIcon,
  InfoIcon,
  MoneyIcon,
  DiscountIcon,
  CreditCardIcon,
  DeliveryIcon,
} from "@shopify/polaris-icons";
import { CircleDollarSignIcon, PercentIcon } from "lucide-react"; // Using lucide-react for icons similar to the image

// Define the interface for a Reward item, matching the structure from your schema.prisma
interface Reward {
  id: string;
  type: string;
  title: string;
  value: number;
  hasExpiry: boolean;
  expiryMonths: number | null; // expiryMonths can be null if hasExpiry is false
}

interface RewardItemProps {
  reward: Reward;
  onEdit: (rewardId: string) => void; // Handler for the Edit button
}

export function RewardItem({ reward, onEdit }: RewardItemProps) {
  // Function to determine the icon based on reward type
  const getRewardIcon = (type: string) => {
    // Using a div to match the HTML structure's icon container styling
    // The HTML uses img, but we'll style a div containing an SVG icon to match the appearance
    const iconComponent = (() => {
      switch (type) {
        case "points": // Assuming type in DB is lowercase based on your modal options
          return <CircleDollarSignIcon size={16} />; // Using lucide-react icon
        case "amount-off": // Assuming type in DB is lowercase
          return <PercentIcon size={16} />; // Using lucide-react icon
        case "store-credit": // Assuming type in DB is lowercase
          return <Icon source={CreditCardIcon} tone="base" />; // Using Polaris icon
        case "free-shipping": // Assuming type in DB is lowercase
          return <Icon source={DeliveryIcon} tone="base" />; // Using Polaris icon
        default:
          return <Icon source={InfoIcon} tone="base" />; // Default icon
      }
    })();

    return (
      <div
        className="flex items-center justify-center w-6 h-6 rounded-full border border-gray-300 text-gray-700 flex-shrink-0" // Tailwind classes for styling
      >
        {iconComponent}
      </div>
    );
  };

  // Function to format the reward value display
  const formatRewardValue = (reward: Reward) => {
    switch (reward.type) {
      case "points":
        return `${reward.value} points`;
      case "amount-off":
        // Assuming value is percentage if title contains '%' or type is 'amount-off' and value is < 100
        // This might need refinement based on your actual data structure
        if (reward.title.includes("%") || (reward.type === "amount-off" && reward.value < 100)) {
          return `${reward.value}% off entire order`;
        } else {
          return `${reward.value}$ off entire order`; // Assuming currency is $
        }
      case "store-credit":
        return `${reward.value} store credits`;
      case "free-shipping":
        return "Free shipping"; // Free shipping usually doesn't have a value number
      default:
        return String(reward.value);
    }
  };

  return (
    <div className="flex items-center justify-between py-4">
      {" "}
      {/* Tailwind classes for flex container */}
      {/* Left side: Icon and Reward Details */}
      <div className="flex items-center gap-3">
        {" "}
        {/* Tailwind classes for flex, items-center, gap */}
        {getRewardIcon(reward.type)}
        <BlockStack gap="100">
          <Text variant="bodyMd" fontWeight="semibold">
            {reward.title}
          </Text>
          <Text variant="bodyMd" tone="subdued">
            {formatRewardValue(reward)}
          </Text>
        </BlockStack>
      </div>
      {/* Right side: Edit Button */}
      <Button variant="plain" icon={EditIcon} onClick={() => onEdit(reward.id)}>
        Edit
      </Button>
    </div>
  );
}
