import { useFetcher, useNavigate } from "@remix-run/react";
import React, { useEffect, useState } from "react";
import type { WaysEarnReward } from "../routes/app.loyalties_._index/interface";
import { ICON_MAP } from "../utils/iconMap";
import StackItem from "./StackItem";

interface StackListProps {
  initialWays: WaysEarnReward[];
}

const StackList: React.FC<StackListProps> = ({ initialWays }) => {
  const fetcher = useFetcher<{ updated: WaysEarnReward }>();
  const navigate = useNavigate();
  const [ways, setWays] = useState<WaysEarnReward[]>(initialWays);

  //
  useEffect(() => {
    setWays(initialWays);
  }, [initialWays]);

  // your existing update effect…
  useEffect(() => {
    if (fetcher.data?.updated) {
      setWays((w) =>
        w.map((item) => (item.id === fetcher.data?.updated.id ? fetcher.data.updated : item)),
      );
    }
  }, [fetcher.data]);

  const handleToggle = (item: WaysEarnReward) => (value: boolean) => {
    const form = new FormData();
    form.append("toggleId", String(item.id));
    form.append("toggleState", value ? "1" : "0");
    fetcher.submit(form, {
      method: "post",
      action: ".", // hits this route's action()
    });

    // Optimistic UI: flip in local state right away
    setWays((w) => w.map((i) => (i.id === item.id ? { ...i, isActive: value } : i)));
  };

  const handleEdit = (item: WaysEarnReward) => () => {
    // Look up the URL in your ICON_MAP by the type code
    const code = item.DefaultWaysEarnRewardType.code;
    const url = ICON_MAP[code]?.url;
    if (url) {
      navigate(url);
    } else {
      console.warn(`No edit URL defined for type ${code}`);
    }
  };

  return (
    <div style={{ marginTop: "16px" }}>
      <div
        style={{
          border: "1px solid #DFE3E8",
          borderRadius: "6px",
          backgroundColor: "#FFF",
          overflow: "hidden",
        }}
      >
        {ways.map((item, i) => (
          <React.Fragment key={item.id}>
            <div
              style={{
                padding: "12px 24px",
                borderBottom: i < ways.length - 1 ? "1px solid #E0E6ED" : "none",
              }}
            >
              <StackItem
                icon={ICON_MAP[item.DefaultWaysEarnRewardType.code].icon}
                title={ICON_MAP[item.DefaultWaysEarnRewardType.code].label}
                subtitle={ICON_MAP[item.DefaultWaysEarnRewardType.code].subLabel}
                active={item.isActive}
                onToggle={handleToggle(item)}
                onEdit={handleEdit(item)}
              />
            </div>
          </React.Fragment>
        ))}
      </div>
    </div>
  );
};

export default StackList;
