// File: app/routes/app.members/route.tsx
import { Outlet, useLoaderData, useNavigation } from "@remix-run/react";
import { TitleBar } from "@shopify/app-bridge-react";
import { Box, Card, Page, Text } from "@shopify/polaris";
import { useTranslation } from "react-i18next";

import { MemberPagination } from "../../components/members/MemberPagination";
import { MemberSearchBar } from "../../components/members/MemberSearchBar";
import { MemberTable } from "../../components/members/MemberTable";
import { useMemberSearch } from "./hooks";
import type { LoaderData } from "./loader";

// Export the loader function from the loader file
export { loader } from "./loader";

/**
 * Main members page component
 */
export default function MembersPage() {
  const { t } = useTranslation();
  const loaderData = useLoaderData<LoaderData>();
  const {
    customers,
    pageInfo,
    currentPage,
    searchQuery,
    searchType,
    totalCustomers,
    errorMessage,
  } = loaderData;

  const navigation = useNavigation();
  const isLoading = navigation.state === "loading";

  // Use custom hook for search and pagination
  const {
    queryValue,
    selectedSearchType,
    isDetailView,
    handleFiltersQueryChange,
    handleSearchTypeChange,
    getPlaceholder,
    handleQueryValueRemove,
    handlePaginationChange,
    handleSearch,
  } = useMemberSearch({
    searchQuery,
    searchType,
    currentPage,
    pageInfo,
  });

  // If we're in detail view, render the Outlet
  if (isDetailView) {
    return <Outlet />;
  }

  return (
    <Page fullWidth>
      <TitleBar title={t("members.title")} />

      <Card padding="200">
        {/* Search Bar */}
        <Box padding="400">
          <MemberSearchBar
            queryValue={queryValue}
            selectedSearchType={selectedSearchType}
            placeholder={getPlaceholder()}
            isLoading={isLoading}
            onQueryChange={handleFiltersQueryChange}
            onSearchTypeChange={handleSearchTypeChange}
            onClearButtonClick={handleQueryValueRemove}
            onSearch={handleSearch}
          />
        </Box>

        {/* Error Message */}
        {errorMessage && (
          <Box>
            <Text tone="critical" as="p">
              {t("members.error.loading")} {errorMessage ?? t("members.error.unknown")}
            </Text>
          </Box>
        )}

        {/* Members Table */}
        <MemberTable customers={customers} isLoading={isLoading} />

        {/* Pagination */}
        <MemberPagination
          customers={customers}
          pageInfo={pageInfo}
          currentPage={currentPage}
          totalCustomers={totalCustomers}
          isLoading={isLoading}
          onPageChange={handlePaginationChange}
        />
      </Card>
    </Page>
  );
}



