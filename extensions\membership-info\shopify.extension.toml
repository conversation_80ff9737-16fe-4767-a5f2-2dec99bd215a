# File: extensions/membership-info/shopify.extension.toml
api_version = "2025-01"

[[extensions]]
name = "Membership"
handle = "membership-info"
type = "ui_extension"

# Capabilities - added required permissions
[extensions.capabilities]
network_access = true
api_access = true
customer_privacy = true

# Define static route for membership path
[[extensions.static_routes]]
path = "membership"
title = "Membership"

# Page extension
[[extensions.targeting]]
module = "./src/MembershipPage.tsx"
target = "customer-account.page.render"
path = "membership"

# Access scopes - this is for documentation but won't work until approved
[access_scopes]
scopes = "customer_read_customers"
