import { WaysEarnRewardType } from "@prisma/client";
import type { LoaderFunctionArgs } from "@remix-run/node";
import db from "../../db.server";
import { authenticate } from "../../shopify.server";
import findShop from "../../utils/find-shop.server";

/**
 * Loader function for the signup program page
 */
export async function loader({ request }: LoaderFunctionArgs) {
  const { admin, session } = await authenticate.admin(request);

  const shop = await findShop(admin, session);

  const waysEarnReward = await db.waysEarnReward.findFirst({
    where: {
      shopId: shop?.id,
      typeEarnReward: WaysEarnRewardType.SIGN_UP,
    },
    include: {
      rewards: true,
    },
  });

  return Response.json({
    waysEarnReward,
  });
}
