import { Outlet, useFetcher, useLoaderData, useLocation } from "@remix-run/react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from "@shopify/polaris";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import type { loader } from "./loader";

const TITLE_MAP_KEYS = {
  points: "loyalties.programTypes.points",
  "store-credit": "loyalties.programTypes.storeCredit",
  vip: "loyalties.programTypes.vip",
};

// Export the loader function and error boundary from their respective files
export { ErrorBoundary } from "./error-boundary";
export { loader } from "./loader";

export default function LoyaltyPage() {
  const { loyaltyProgram, loyaltyProgramTypeInDb } = useLoaderData<typeof loader>();
  const location = useLocation();
  const [isActive, setIsActive] = useState(loyaltyProgram?.isActive ?? false);
  const [title, setTitle] = useState("");
  const fetcher = useFetcher();
  const { t } = useTranslation();

  const handleToggleActive = () => {
    const newActiveState = !isActive;
    setIsActive(newActiveState);

    // Create form data to submit
    const formData = new FormData();
    formData.append("isActive", newActiveState.toString());
    formData.append("programType", loyaltyProgramTypeInDb);

    // Submit the form data to the action using fetcher
    fetcher.submit(formData, { method: "post" });
  };

  useEffect(() => {
    const pathKey = location.pathname.split("/").pop() as keyof typeof TITLE_MAP_KEYS;
    const translationKey = TITLE_MAP_KEYS[pathKey] ?? "";
    if (translationKey) {
      setTitle(t(translationKey));
    } else {
      setTitle("");
    }
  }, [location.pathname, t]);

  return (
    <Page
      backAction={{ content: t("loyalties.backToPoints"), url: "/app/loyalties" }}
      title={title}
      titleMetadata={
        isActive ? (
          <Badge tone="success">{t("loyalties.status.active")}</Badge>
        ) : (
          <Badge>{t("loyalties.status.inactive")}</Badge>
        )
      }
      primaryAction={
        <Button onClick={handleToggleActive}>
          {isActive ? t("loyalties.status.deactivate") : t("loyalties.status.activate")}
        </Button>
      }
    >
      <p>
        {t("loyalties.status.statusMessage", {
          program: title,
          status: isActive
            ? t("loyalties.status.active").toLowerCase()
            : t("loyalties.status.inactive").toLowerCase(),
        })}
      </p>
      <div className="mt-5">
        <Outlet />
      </div>
    </Page>
  );
}
