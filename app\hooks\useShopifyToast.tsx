import { useAppBridge } from "@shopify/app-bridge-react";

const DEFAULT_TOAST_DURATION = 2000;

export const useShopifyToast = () => {
  const shopify = useAppBridge();

  const showToast = (message: string, options?: { isError?: boolean; duration?: number }) => {
    shopify?.toast.show(message, options);
  };

  const showSuccessToast = (message: string) => {
    showToast(message, { isError: false, duration: DEFAULT_TOAST_DURATION });
  };

  const showErrorToast = (message: string) => {
    showToast(message, { isError: true, duration: DEFAULT_TOAST_DURATION });
  };

  return { showToast, showSuccessToast, showErrorToast };
};
