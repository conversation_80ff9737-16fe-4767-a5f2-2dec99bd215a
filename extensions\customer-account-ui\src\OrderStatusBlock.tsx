import {
  Banner,
  BlockStack,
  reactExtension,
  TextBlock,
  useApi,
} from "@shopify/ui-extensions-react/customer-account";
import { useEffect } from "react";

export default reactExtension("customer-account.page.render", () => <PromotionBanner />);

function PromotionBanner() {
  const { i18n, sessionToken } = useApi();

  const getCustomerNameQuery = {
    query: `query {
      customer {
        firstName
        displayName
      }
    }`,
  };

  const fetchData = async () => {
    const token = await sessionToken.get();
    console.log("sessionToken", token);

    fetch("https://cheers-sm-foto-patient.trycloudflare.com/api/extensions/customer", {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
        "Access-Control-Allow-Origin": "*",
        Accept: "application/json",
      },
    })
      .then((response) => response.json())
      .then((data) => {
        console.log(data);
      })
      .catch(console.error);
  };

  useEffect(() => {
    fetch("shopify://customer-account/api/2025-04/graphql.json", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(getCustomerNameQuery),
    })
      .then((response) => response.json())
      .then(({ data }) => {
        console.log(data);
      })
      .catch(console.error);

    fetchData();
  });

  return (
    <Banner>
      <BlockStack inlineAlignment="center">
        <TextBlock>{i18n.translate("earnPoints")}</TextBlock>
      </BlockStack>
    </Banner>
  );
}
