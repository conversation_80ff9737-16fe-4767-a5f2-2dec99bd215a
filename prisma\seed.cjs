// prisma/seed.cjs
const { PrismaClient } = require("@prisma/client");
const db = new PrismaClient();
/* … rest of your upsert logic … */

async function main() {
  console.log("Seeding DefaultWaysEarnRewardType…");
  const defaults = [
    { code: "PURCHASE" },
    { code: "SIGN_UP" },
    { code: "REFERRAL" },
    { code: "COMPLETE_PROFILE" },
    { code: "CUSTOM_REWARD" },
    { code: "CELEBRATE_BIRTHDAY" },
  ];

  for (const d of defaults) {
    await db.defaultWaysEarnRewardType.upsert({
      where: { code: d.code },
      update: {},
      create: d,
    });
  }

  console.log("✅  Seed complete");
}

main()
  .catch((e) => {
    console.error(e);
  })
  .finally(() => db.$disconnect());
