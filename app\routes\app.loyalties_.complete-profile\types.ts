// Local enum to match Prisma's SignupRewardType
import { DiscountType, RewardType, SignupRewardType } from "@prisma/client";
import { RewardTypeInterface } from "../../components/RewardsSection";
export interface Gender {
  id: number;
  name: string;
}

export type ShopType = {
  id: number;
  shopId: string;
  myshopifyDomain: string;
  [key: string]: unknown;
};
export type BaseRewardDataType = {
  title: string;
  type: SignupRewardType;
  value: string | null;
};
export enum LocalDiscountType {
  PERCENTAGE = "PERCENTAGE",
  FIXED = "FIXED",
}

export enum LocalRequirementType {
  NONE = "NONE",
  AMOUNT = "AMOUNT",
  QUANTITY = "QUANTITY",
}

export enum MinRequirementType {
  NO_MINIMUM = "NO_MINIMUM",
  MIN_PURCHASE_AMOUNT = "MIN_PURCHASE_AMOUNT",
  MIN_QUANTITY_ITEMS = "MIN_QUANTITY_ITEMS",
}

export enum LocalRewardType {
  POINTS = "POINTS",
  STORE_CREDIT = "STORE_CREDIT",
  AMOUNT_OFF = "AMOUNT_OFF",
  FREE_SHIPPING = "FREE_SHIPPING",
}
export interface RewardData {
  title: string;
  type: RewardType;
  value: string | null;
  completeProfileSettingsId: number;
  discountType?: DiscountType | null;
  minRequirementType?: MinRequirementType | null;
  minRequirementValue?: number | null;
  productDiscounts?: boolean;
  orderDiscounts?: boolean;
  shippingDiscounts?: boolean;
}

export interface ValidatedRequestData {
  pageTitle: string;
  pageStatus: string;
  rewards: RewardTypeInterface[];
}
export interface CustomProfile {
  id: number;
  editable: boolean;
  gender: Gender[];
}

export interface CompleteProfileSettings {
  id: number;
  pageTitle: string;
  isActive: boolean;
  customProfile: CustomProfile;
  rewards: Reward[];
}

export interface Reward {
  id: number;
  title: string;
  value: string | null;
  completeProfileSettingsId: number;
  minRequirementValue: number;
  type: RewardType;
  productDiscounts?: boolean;
  orderDiscounts?: boolean;
  shippingDiscounts?: boolean;
  discountType: DiscountType | null;
  minRequirementType: MinRequirementType | null;
}

export interface LoaderData {
  initialRewards: Reward[];
  completeProfileSettings: CompleteProfileSettings;
}

export interface ActionResponse {
  success: boolean;
  error?: string;
  completeProfileSettings?: {
    id: number;
    pageTitle: string;
    customProfile: CustomProfile;
    rewards: Reward[];
  };
}
