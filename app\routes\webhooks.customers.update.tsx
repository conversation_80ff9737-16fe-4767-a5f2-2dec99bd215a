import type { ActionFunctionArgs } from "@remix-run/node";
import { authenticate } from "../shopify.server";
import { MemberMetafield } from "../types/memberTypes";

import db from "../db.server";
import {
  getNamespaceMetafield,
  updateCustomerCompleteMetafield,
} from "./webhooks.returns.close/services";

export const action = async ({ request }: ActionFunctionArgs) => {
  const { payload, topic, shop, admin } = await authenticate.webhook(request);
  console.log(`Received ${topic} webhook for ${shop}`);

  const shopInfo = await db.shop.findFirst({ where: { myshopifyDomain: shop } });
  if (!shopInfo) {
    throw new Error("Shop not found");
  }

  const completeProfileSettings = await db.completeProfileSettings.findFirst({
    where: { shopId: shopInfo.id },
    include: {
      rewards: true,
      customProfile: {
        include: {
          gender: true,
        },
      },
    },
  });
  const rewardPoint =
    completeProfileSettings?.rewards.find((item) => item.type === "POINTS")?.value ?? 0;

  if (Number(rewardPoint) > 0 && admin) {
    const response = await admin.graphql(
      `query getCustomer($id: ID!) {
            customer(id: $id) {
                id
                metafields(first: 10) {
                    edges {
                        node {
                        namespace
                        key
                        value
                        }
                    }
                }
                amountSpent {amount currencyCode}
            }
        }`,
      { variables: { id: payload?.admin_graphql_api_id } },
    );
    const responseJson = await response.json();
    const customer = responseJson?.data?.customer;
    const namespace = (await getNamespaceMetafield(admin)) as string;

    // Extract metafields
    const metafields = customer.metafields?.edges.map((m: any) => ({
      namespace: m.node.namespace,
      key: m.node.key,
      value: m.node.value,
    })) as MemberMetafield[];

    // Find genders metafield
    const genderMetafield = metafields.find((m) => m.namespace === namespace && m.key === "gender");
    const gender = genderMetafield ? genderMetafield.value : null;

    // Find points metafield
    const pointsMetafield = metafields.find((m) => m.namespace === namespace && m.key === "points");
    const customerPoints = pointsMetafield ? pointsMetafield.value : 0;

    // Find points metafield
    const isCompletedProfileMetafield = metafields.find(
      (m) => m.namespace === namespace && m.key === "isCompletedProfile",
    );
    const isCompletedProfile = isCompletedProfileMetafield
      ? isCompletedProfileMetafield.value
      : false;

    // Find points metafield
    const birthdayMetafield = metafields.find(
      (m) => m.namespace === namespace && m.key === "birthday",
    );
    const birthday = birthdayMetafield ? birthdayMetafield.value : null;

    //update Customer Point && isCompletedProfile
    if (gender && birthday && !isCompletedProfile) {
      const newPoint = Number(customerPoints) + Number(rewardPoint);
      await updateCustomerCompleteMetafield(admin, customer.id, String(newPoint), namespace);
    }
  }
  return new Response();
};
