import { useFetcher, useLoaderD<PERSON>, useNavigate } from "@remix-run/react";
import { useAppBridge } from "@shopify/app-bridge-react";
import {
  BlockStack,
  Button,
  Card,
  Form,
  InlineStack,
  Layout,
  Page,
  Text,
  TextField,
} from "@shopify/polaris";
import { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import {
  DISCOUNT_TYPES,
  PointRewardInterface,
  REQUIREMENT_TYPES,
  REWARD_TYPES,
  RewardsSection,
  RewardTypeInterface,
} from "../../components/RewardsSection";
import type { LoaderData } from "./loader";

// Local enums to replace imports from @prisma/client
enum LocalRewardType {
  POINTS = "POINTS",
  STORE_CREDIT = "STORE_CREDIT",
  AMOUNT_OFF = "AMOUNT_OFF",
  FREE_SHIPPING = "FREE_SHIPPING",
}

enum LocalDiscountType {
  PERCENTAGE = "PERCENTAGE",
  FIXED = "FIXED",
}

enum LocalRequirementType {
  NONE = "NONE",
  AMOUNT = "AMOUNT",
  QUANTITY = "QUANTITY",
}

// Export the action and loader functions
export { action } from "./actions";
export { loader } from "./loader";

export default function LoyaltyVipEditPage() {
  const { vipTier } = useLoaderData<LoaderData>();
  const shopify = useAppBridge();
  const navigate = useNavigate();
  const fetcher = useFetcher();
  const { t } = useTranslation();

  // Initialize state with the tier data
  const [goal, setGoal] = useState(vipTier.spendRequirement?.toString() ?? "0");
  const [tierName, setTierName] = useState(vipTier.name);

  // Helper function to get requirement type
  const getRequirementType = useCallback((requirementType: LocalRequirementType | null) => {
    if (requirementType === LocalRequirementType.AMOUNT) {
      return REQUIREMENT_TYPES.AMOUNT;
    } else if (requirementType === LocalRequirementType.QUANTITY) {
      return REQUIREMENT_TYPES.QUANTITY;
    } else {
      return REQUIREMENT_TYPES.NONE;
    }
  }, []);

  // Convert database rewards to the format used by the UI
  const mapDbRewardsToUiRewards = useCallback(
    (dbRewards: LoaderData["vipTier"]["rewards"]) => {
      return dbRewards.map((reward: LoaderData["vipTier"]["rewards"][0]) => {
        const baseReward = {
          id: reward.id.toString(),
          title: reward.title,
          value: reward.value ?? "0",
        };

        switch (reward.rewardType) {
          case LocalRewardType.POINTS:
            return {
              ...baseReward,
              type: REWARD_TYPES.POINTS,
            } as PointRewardInterface;
          case LocalRewardType.STORE_CREDIT:
            return {
              ...baseReward,
              type: REWARD_TYPES.STORE_CREDIT,
            };
          case LocalRewardType.AMOUNT_OFF:
            return {
              ...baseReward,
              type: REWARD_TYPES.AMOUNT_OFF,
              discountType:
                reward.discountType === LocalDiscountType.PERCENTAGE
                  ? DISCOUNT_TYPES.PERCENTAGE
                  : DISCOUNT_TYPES.FIXED,
              minimumRequirement: getRequirementType(
                reward.minimumRequirement as LocalRequirementType | null,
              ),
              minimumValue: reward.minimumValue?.toString(),
              combinations: {
                productDiscounts: reward.productDiscounts ?? false,
                orderDiscounts: reward.orderDiscounts ?? false,
                shippingDiscounts: reward.shippingDiscounts ?? false,
              },
            };
          case LocalRewardType.FREE_SHIPPING:
            return {
              ...baseReward,
              type: REWARD_TYPES.FREE_SHIPPING,
              minimumRequirement: getRequirementType(
                reward.minimumRequirement as LocalRequirementType | null,
              ),
              minimumValue: reward.minimumValue?.toString(),
              combinations: {
                productDiscounts: reward.productDiscounts ?? false,
                orderDiscounts: reward.orderDiscounts ?? false,
              },
            };
          default:
            // For any unknown reward type, treat it as a points reward
            return {
              ...baseReward,
              type: REWARD_TYPES.POINTS,
            } as PointRewardInterface;
        }
      });
    },
    [getRequirementType],
  );

  const [rewards, setRewards] = useState<RewardTypeInterface[]>(() =>
    mapDbRewardsToUiRewards(vipTier.rewards),
  );

  const handleUpdateVipTier = useCallback(() => {
    // Create JSON data to submit
    const jsonData = {
      tierId: vipTier.id,
      tierName,
      spendRequirement: goal,
      rewards,
    };

    // Submit the JSON data to the action using fetcher
    fetcher.submit(JSON.stringify(jsonData), { method: "post", encType: "application/json" });
  }, [vipTier.id, tierName, goal, rewards, fetcher]);

  // Define the response type
  interface ActionResponse {
    success: boolean;
    error?: string;
    vipTier?: {
      id: number;
      name: string;
      spendRequirement: number | null;
      rewards: Array<{
        id: number;
        title: string;
        rewardType: LocalRewardType;
        value: string | null;
      }>;
    };
  }

  // Handle the response from the action
  useEffect(() => {
    if (fetcher.data) {
      const responseData = fetcher.data as ActionResponse;
      if (responseData.success) {
        // Use App Bridge Toast API for success message
        shopify?.toast.show(t("loyalties.vip.toastMessages.tierUpdated"), {
          isError: false,
          duration: 4500,
        });

        // Navigate back to the VIP tiers list after a short delay
        setTimeout(() => {
          navigate("/app/loyalties/vip");
        }, 1500);
      } else {
        // Use App Bridge Toast API for error message
        shopify?.toast.show(
          responseData.error ?? t("loyalties.vip.toastMessages.tierUpdateFailed"),
          {
            isError: true,
            duration: 4500,
          },
        );
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fetcher.data, navigate]);

  const handleSubmit = useCallback(() => {
    handleUpdateVipTier();
  }, [handleUpdateVipTier]);

  return (
    <Page
      backAction={{
        content: t("loyalties.vip.editTier.backAction"),
        url: "/app/loyalties/vip",
      }}
      title={t("loyalties.vip.editTier.title")}
    >
      <Form onSubmit={handleSubmit}>
        <Layout>
          <Layout.Section>
            <Card>
              <BlockStack gap={"400"}>
                <Text as="h2" variant="headingMd">
                  {t("loyalties.vip.editTier.tierName")}
                </Text>
                <TextField
                  label=""
                  value={tierName}
                  onChange={setTierName}
                  autoComplete="off"
                  disabled={fetcher.state === "submitting"}
                />
              </BlockStack>
            </Card>
          </Layout.Section>

          <Layout.Section>
            <Card>
              <BlockStack gap="400">
                <Text variant="headingMd" as="h2">
                  {t("loyalties.vip.editTier.entryGoal")}
                </Text>

                <InlineStack gap="200" align="start" blockAlign="center">
                  <div style={{ width: "150px" }}>
                    <TextField
                      label=""
                      type="number"
                      value={goal}
                      onChange={setGoal}
                      autoComplete="off"
                      min={0}
                      labelHidden
                      prefix="$"
                      disabled={fetcher.state === "submitting"}
                    />
                  </div>
                  <Text as="span" variant="bodyMd">
                    {t("loyalties.vip.editTier.spentSinceStartDate")}
                  </Text>
                </InlineStack>
              </BlockStack>
            </Card>
          </Layout.Section>

          <Layout.Section>
            <RewardsSection
              isSubmitting={fetcher.state === "submitting"}
              onRewardsChange={setRewards}
              initialRewards={rewards}
              titleText={t("loyalties.vip.editTier.rewards.title")}
              addNewText={t("loyalties.vip.editTier.rewards.addNew")}
              descriptionText={t("loyalties.vip.editTier.rewards.description")}
            />
          </Layout.Section>

          <Layout.Section>
            <div className="flex justify-center w-full items-center mb-5">
              <Button
                size="large"
                variant="primary"
                onClick={handleUpdateVipTier}
                submit
                loading={fetcher.state === "submitting"}
                disabled={fetcher.state === "submitting"}
              >
                {fetcher.state === "submitting"
                  ? t("loyalties.vip.editTier.saving")
                  : t("loyalties.vip.editTier.save")}
              </Button>
            </div>
          </Layout.Section>
        </Layout>
      </Form>
    </Page>
  );
}
