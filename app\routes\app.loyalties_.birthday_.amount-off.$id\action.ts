import type { LoaderFunctionArgs } from "@remix-run/node";
import db from "../../db.server";
import { authenticate } from "../../shopify.server";

// Helper function to create Shopify discount
async function createShopifyDiscount(admin: any, discountData: any) {
  const mutation = `
    mutation discountAutomaticBasicCreate($automaticBasicDiscount: DiscountAutomaticBasicInput!) {
      discountAutomaticBasicCreate(automaticBasicDiscount: $automaticBasicDiscount) {
        automaticDiscountNode {
          id
          automaticDiscount {
            ... on DiscountAutomaticBasic {
              title
              startsAt
              endsAt
              status
              summary
            }
          }
        }
        userErrors {
          field
          message
        }
      }
    }
  `;

  const variables = {
    automaticBasicDiscount: discountData,
  };

  const response = await admin.graphql(mutation, { variables });
  return await response.json();
}

// Helper function to update Shopify discount
async function updateShopifyDiscount(admin: any, discountId: string, discountData: any) {
  const mutation = `
    mutation discountAutomaticBasicUpdate($id: ID!, $automaticBasicDiscount: DiscountAutomaticBasicInput!) {
      discountAutomaticBasicUpdate(id: $id, automaticBasicDiscount: $automaticBasicDiscount) {
        automaticDiscountNode {
          id
          automaticDiscount {
            ... on DiscountAutomaticBasic {
              title
              startsAt
              endsAt
              status
              summary
            }
          }
        }
        userErrors {
          field
          message
        }
      }
    }
  `;

  const variables = {
    id: discountId,
    automaticBasicDiscount: discountData,
  };

  const response = await admin.graphql(mutation, { variables });
  return await response.json();
}

// Helper function to delete Shopify discount
async function deleteShopifyDiscount(admin: any, discountId: string) {
  const mutation = `
    mutation discountAutomaticDelete($id: ID!) {
      discountAutomaticDelete(id: $id) {
        deletedAutomaticDiscountId
        userErrors {
          field
          message
        }
      }
    }
  `;

  const variables = {
    id: discountId,
  };

  const response = await admin.graphql(mutation, { variables });
  return await response.json();
}

// Helper function to format discount data for Shopify
function formatDiscountForShopify(rewardData: any, tierName?: string) {
  const {
    title,
    rewardValue,
    discountType,
    minimumRequirement,
    minimumValue,
    expiryMonths,
    combinations,
  } = rewardData;

  // Calculate end date if expiry is set
  let endsAt = null;
  if (expiryMonths) {
    const endDate = new Date();
    endDate.setMonth(endDate.getMonth() + expiryMonths);
    endsAt = endDate.toISOString();
  }

  // Set up discount value
  let discountValue;
  if (discountType === "PERCENTAGE" || discountType === "percentage") {
    discountValue = {
      percentage: rewardValue / 100,
    };
  } else {
    discountValue = {
      discountAmount: {
        amount: rewardValue.toString(),
        appliesOnEachItem: false,
      },
    };
  }

  const discountTitle = tierName
    ? `${title || "Birthday Discount"} - ${tierName}`
    : title || "Birthday Discount";

  // Set up minimum requirement - FIXED STRUCTURE
  let minimumRequirementData = undefined;
  if (minimumRequirement === "AMOUNT" || minimumRequirement === "amount") {
    minimumRequirementData = {
      subtotal: {
        greaterThanOrEqualToSubtotal: minimumValue?.toString() || "0",
      },
    };
  } else if (minimumRequirement === "QUANTITY" || minimumRequirement === "quantity") {
    minimumRequirementData = {
      quantity: {
        greaterThanOrEqualToQuantity: minimumValue?.toString() || "0",
      },
    };
  }

  // Set up discount combinations - CRITICAL FOR COMBINING DISCOUNTS
  const combinesWith = {
    orderDiscounts: combinations?.order ?? true,
    productDiscounts: combinations?.product ?? true,
    shippingDiscounts: combinations?.shipping ?? true,
  };

  const discountData = {
    title: discountTitle,
    startsAt: new Date().toISOString(),
    ...(endsAt && { endsAt }),
    customerGets: {
      value: discountValue,
      items: {
        all: true,
      },
    },
    ...(minimumRequirementData && { minimumRequirement: minimumRequirementData }),
    combinesWith: combinesWith, // Enable discount combinations
  };

  return discountData;
}

// Helper function to create tier-based discounts
async function createTierBasedDiscounts(
  admin: any,
  settingsId: number,
  title: string,
  expiryMonths: number | undefined,
  tierDiscounts: any[],
  combinations: any,
) {
  const results = [];
  const errors = [];

  for (const tierDiscount of tierDiscounts) {
    try {
      // Get tier information
      const tier = await db.loyaltyVIPTier.findUnique({
        where: { id: tierDiscount.tierId },
        select: { name: true },
      });

      if (!tier) {
        console.warn(`Tier ${tierDiscount.tierId} not found`);
        continue;
      }

      // Format discount data for Shopify
      const shopifyDiscountData = formatDiscountForShopify(
        {
          title,
          rewardValue: tierDiscount.rewardValue,
          discountType: tierDiscount.discountType,
          minimumRequirement: tierDiscount.minimumRequirement,
          minimumValue: tierDiscount.minimumValue,
          expiryMonths,
          combinations, // Pass combinations data
        },
        tier.name,
      );

      // Create discount in Shopify
      const shopifyResponse = await createShopifyDiscount(admin, shopifyDiscountData);

      if (shopifyResponse.data?.discountAutomaticBasicCreate?.userErrors?.length > 0) {
        const errorMessage = `Shopify Error for tier ${tier.name}: ${shopifyResponse.data.discountAutomaticBasicCreate.userErrors
          .map((error: any) => error.message)
          .join(", ")}`;
        console.error(errorMessage);
        errors.push(errorMessage);
        continue;
      }

      const shopifyDiscountId =
        shopifyResponse.data?.discountAutomaticBasicCreate?.automaticDiscountNode?.id;

      // Create the birthday reward in local database for this tier
      const birthdayReward = await db.birthdayReward.create({
        data: {
          settingsId: Number(settingsId),
          birthdayRewardType: "AMOUNT_OFF",
          title: `${title} - ${tier.name}`,
          expiryMonths: expiryMonths ? Number(expiryMonths) : undefined,
          rewardValue: Number(tierDiscount.rewardValue),
          discountType: tierDiscount.discountType.toUpperCase(),
          minimumRequirement: tierDiscount.minimumRequirement.toUpperCase(),
          minimumValue: tierDiscount.minimumValue,
          productDiscounts: combinations.product,
          orderDiscounts: combinations.order,
          shippingDiscounts: combinations.shipping,
          shopifyDiscountId: shopifyDiscountId,
        },
        select: {
          id: true,
          birthdayRewardType: true,
          title: true,
          expiryMonths: true,
          rewardValue: true,
          discountType: true,
          minimumRequirement: true,
          minimumValue: true,
          productDiscounts: true,
          orderDiscounts: true,
          shippingDiscounts: true,
          shopifyDiscountId: true,
        },
      });

      results.push(birthdayReward);
    } catch (error) {
      const errorMessage = `Error creating discount for tier ${tierDiscount.tierId}: ${error instanceof Error ? error.message : "Unknown error"}`;
      console.error(errorMessage);
      errors.push(errorMessage);
    }
  }

  return { results, errors };
}

// Helper function to update tier-based discounts
async function updateTierBasedDiscounts(
  admin: any,
  existingSettingsId: number,
  title: string,
  expiryMonths: number | undefined,
  tierDiscounts: any[],
  combinations: any,
) {
  // First, delete all existing tier-based discounts for this settings
  const existingRewards = await db.birthdayReward.findMany({
    where: {
      settingsId: existingSettingsId,
      birthdayRewardType: "AMOUNT_OFF",
    },
    select: {
      id: true,
      shopifyDiscountId: true,
      title: true,
    },
  });

  // Delete Shopify discounts
  for (const reward of existingRewards) {
    if (reward.shopifyDiscountId) {
      try {
        await deleteShopifyDiscount(admin, reward.shopifyDiscountId);
      } catch (error) {
        console.warn(`Failed to delete Shopify discount for ${reward.title}:`, error);
      }
    }
  }

  // Delete local database records
  await db.birthdayReward.deleteMany({
    where: {
      settingsId: existingSettingsId,
      birthdayRewardType: "AMOUNT_OFF",
    },
  });

  // Create new tier-based discounts
  return await createTierBasedDiscounts(
    admin,
    existingSettingsId,
    title,
    expiryMonths,
    tierDiscounts,
    combinations,
  );
}

export const action = async ({ request }: LoaderFunctionArgs) => {
  const { admin } = await authenticate.admin(request);

  if (request.method === "POST") {
    const requestBody = await request.json();
    const {
      settingsId,
      title,
      expiryMonths,
      combinations,
      hasDifferentTiers,
      // Single discount fields
      rewardValue,
      discountType,
      minimumRequirement,
      minimumValue,
      // Tier discount fields
      tierDiscounts,
    } = requestBody;

    try {
      if (hasDifferentTiers && tierDiscounts && tierDiscounts.length > 0) {
        // Create tier-based discounts
        const { results, errors } = await createTierBasedDiscounts(
          admin,
          settingsId,
          title,
          expiryMonths,
          tierDiscounts,
          combinations,
        );

        if (errors.length > 0) {
          return {
            success: false,
            error: `Some discounts failed to create: ${errors.join("; ")}`,
            discounts: results,
          };
        }

        return {
          success: true,
          message: `Created ${results.length} tier-based discounts successfully`,
          discounts: results,
        };
      } else {
        // Create single discount
        const shopifyDiscountData = formatDiscountForShopify({
          title,
          rewardValue,
          discountType,
          minimumRequirement,
          minimumValue,
          expiryMonths,
          combinations, // Pass combinations data
        });

        // Create discount in Shopify
        const shopifyResponse = await createShopifyDiscount(admin, shopifyDiscountData);

        if (shopifyResponse.data?.discountAutomaticBasicCreate?.userErrors?.length > 0) {
          throw new Error(
            `Shopify Error: ${shopifyResponse.data.discountAutomaticBasicCreate.userErrors
              .map((error: any) => error.message)
              .join(", ")}`,
          );
        }

        const shopifyDiscountId =
          shopifyResponse.data?.discountAutomaticBasicCreate?.automaticDiscountNode?.id;

        // Create the birthday reward in local database
        const birthdayReward = await db.birthdayReward.create({
          data: {
            settingsId: Number(settingsId),
            birthdayRewardType: "AMOUNT_OFF",
            title: title,
            expiryMonths: expiryMonths ? Number(expiryMonths) : undefined,
            rewardValue: Number(rewardValue),
            discountType: discountType.toUpperCase(),
            minimumRequirement: minimumRequirement.toUpperCase(),
            minimumValue: minimumValue,
            productDiscounts: combinations.product,
            orderDiscounts: combinations.order,
            shippingDiscounts: combinations.shipping,
            shopifyDiscountId: shopifyDiscountId,
          },
          select: {
            id: true,
            birthdayRewardType: true,
            title: true,
            expiryMonths: true,
            rewardValue: true,
            discountType: true,
            minimumRequirement: true,
            minimumValue: true,
            productDiscounts: true,
            orderDiscounts: true,
            shippingDiscounts: true,
            shopifyDiscountId: true,
          },
        });

        return {
          success: true,
          message: "Discount created successfully",
          ...birthdayReward,
        };
      }
    } catch (error) {
      console.error("Error creating birthday reward:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to create discount",
      };
    }
  } else if (request.method === "PATCH") {
    const requestBody = await request.json();
    const {
      id,
      title,
      expiryMonths,
      combinations,
      hasDifferentTiers,
      // Single discount fields
      rewardValue,
      discountType,
      minimumRequirement,
      minimumValue,
      // Tier discount fields
      tierDiscounts,
    } = requestBody;

    try {
      if (hasDifferentTiers && tierDiscounts && tierDiscounts.length > 0) {
        // Get existing reward to get settingsId
        const existingReward = await db.birthdayReward.findUnique({
          where: { id: Number(id) },
          select: { settingsId: true },
        });

        if (!existingReward) {
          return { success: false, error: "Reward not found" };
        }

        // Update to tier-based discounts
        const { results, errors } = await updateTierBasedDiscounts(
          admin,
          existingReward.settingsId,
          title,
          expiryMonths,
          tierDiscounts,
          combinations,
        );

        if (errors.length > 0) {
          return {
            success: false,
            error: `Some discounts failed to update: ${errors.join("; ")}`,
            discounts: results,
          };
        }

        return {
          success: true,
          message: `Updated to tier-based discounts (${results.length} tiers)`,
          discounts: results,
        };
      } else {
        // Update single discount
        const existingReward = await db.birthdayReward.findUnique({
          where: { id: Number(id) },
          select: { shopifyDiscountId: true, settingsId: true },
        });

        if (!existingReward) {
          return { success: false, error: "Reward not found" };
        }

        // If switching from tier-based to single discount, clean up tier discounts first
        const allRewardsInSettings = await db.birthdayReward.findMany({
          where: {
            settingsId: existingReward.settingsId,
            birthdayRewardType: "AMOUNT_OFF",
          },
          select: {
            id: true,
            shopifyDiscountId: true,
            title: true,
          },
        });

        if (allRewardsInSettings.length > 1) {
          // Clean up multiple tier discounts and convert to single
          for (const reward of allRewardsInSettings) {
            if (reward.shopifyDiscountId) {
              try {
                await deleteShopifyDiscount(admin, reward.shopifyDiscountId);
              } catch (error) {
                console.warn(`Failed to delete Shopify discount for ${reward.title}:`, error);
              }
            }
          }

          // Delete all existing records
          await db.birthdayReward.deleteMany({
            where: {
              settingsId: existingReward.settingsId,
              birthdayRewardType: "AMOUNT_OFF",
            },
          });

          // Create new single discount
          const shopifyDiscountData = formatDiscountForShopify({
            title,
            rewardValue,
            discountType,
            minimumRequirement,
            minimumValue,
            expiryMonths,
            combinations, // Pass combinations data
          });

          const shopifyResponse = await createShopifyDiscount(admin, shopifyDiscountData);

          if (shopifyResponse.data?.discountAutomaticBasicCreate?.userErrors?.length > 0) {
            throw new Error(
              `Shopify Error: ${shopifyResponse.data.discountAutomaticBasicCreate.userErrors
                .map((error: any) => error.message)
                .join(", ")}`,
            );
          }

          const shopifyDiscountId =
            shopifyResponse.data?.discountAutomaticBasicCreate?.automaticDiscountNode?.id;

          const birthdayReward = await db.birthdayReward.create({
            data: {
              settingsId: existingReward.settingsId,
              birthdayRewardType: "AMOUNT_OFF",
              title: title,
              expiryMonths: expiryMonths ? Number(expiryMonths) : undefined,
              rewardValue: Number(rewardValue),
              discountType: discountType.toUpperCase(),
              minimumRequirement: minimumRequirement.toUpperCase(),
              minimumValue: minimumValue,
              productDiscounts: combinations.product,
              orderDiscounts: combinations.order,
              shippingDiscounts: combinations.shipping,
              shopifyDiscountId: shopifyDiscountId,
            },
            select: {
              id: true,
              birthdayRewardType: true,
              title: true,
              expiryMonths: true,
              rewardValue: true,
              discountType: true,
              minimumRequirement: true,
              minimumValue: true,
              productDiscounts: true,
              orderDiscounts: true,
              shippingDiscounts: true,
              shopifyDiscountId: true,
            },
          });

          return {
            success: true,
            message: "Converted from tier-based to single discount successfully",
            ...birthdayReward,
          };
        } else {
          // Update existing single discount
          if (existingReward.shopifyDiscountId) {
            const shopifyDiscountData = formatDiscountForShopify({
              title,
              rewardValue,
              discountType,
              minimumRequirement,
              minimumValue,
              expiryMonths,
              combinations, // Pass combinations data
            });

            const shopifyResponse = await updateShopifyDiscount(
              admin,
              existingReward.shopifyDiscountId,
              shopifyDiscountData,
            );

            if (shopifyResponse.data?.discountAutomaticBasicUpdate?.userErrors?.length > 0) {
              throw new Error(
                `Shopify Error: ${shopifyResponse.data.discountAutomaticBasicUpdate.userErrors
                  .map((error: any) => error.message)
                  .join(", ")}`,
              );
            }
          }

          // Update the birthday reward in local database
          const birthdayReward = await db.birthdayReward.update({
            where: {
              id: Number(id),
            },
            data: {
              title: title,
              expiryMonths: expiryMonths ? Number(expiryMonths) : undefined,
              rewardValue: Number(rewardValue),
              discountType: discountType.toUpperCase(),
              minimumRequirement: minimumRequirement.toUpperCase(),
              minimumValue: minimumValue,
              productDiscounts: combinations.product,
              orderDiscounts: combinations.order,
              shippingDiscounts: combinations.shipping,
            },
            select: {
              id: true,
              birthdayRewardType: true,
              title: true,
              expiryMonths: true,
              rewardValue: true,
              discountType: true,
              minimumRequirement: true,
              minimumValue: true,
              productDiscounts: true,
              orderDiscounts: true,
              shippingDiscounts: true,
              shopifyDiscountId: true,
            },
          });

          return {
            success: true,
            message: "Discount updated successfully",
            ...birthdayReward,
          };
        }
      }
    } catch (error) {
      console.error("Error updating birthday reward:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to update discount",
      };
    }
  } else if (request.method === "DELETE") {
    const { id } = await request.json();

    try {
      // Get existing reward to get Shopify discount ID and settingsId
      const existingReward = await db.birthdayReward.findUnique({
        where: { id: Number(id) },
        select: { shopifyDiscountId: true, settingsId: true },
      });

      if (!existingReward) {
        return { success: false, error: "Reward not found" };
      }

      // Check if there are multiple rewards with same settingsId (tier-based)
      const allRewardsInSettings = await db.birthdayReward.findMany({
        where: {
          settingsId: existingReward.settingsId,
          birthdayRewardType: "AMOUNT_OFF",
        },
        select: {
          id: true,
          shopifyDiscountId: true,
          title: true,
        },
      });

      const errors = [];

      // Delete all associated Shopify discounts
      for (const reward of allRewardsInSettings) {
        if (reward.shopifyDiscountId) {
          try {
            const shopifyResponse = await deleteShopifyDiscount(admin, reward.shopifyDiscountId);

            if (shopifyResponse.data?.discountAutomaticDelete?.userErrors?.length > 0) {
              const warningMessage = `Shopify Warning for ${reward.title}: ${shopifyResponse.data.discountAutomaticDelete.userErrors
                .map((error: any) => error.message)
                .join(", ")}`;
              console.warn(warningMessage);
              errors.push(warningMessage);
            }
          } catch (error) {
            const errorMessage = `Failed to delete Shopify discount for ${reward.title}: ${error instanceof Error ? error.message : "Unknown error"}`;
            console.warn(errorMessage);
            errors.push(errorMessage);
          }
        }
      }

      // Delete all birthday rewards for this settings
      const deletedRewards = await db.birthdayReward.deleteMany({
        where: {
          settingsId: existingReward.settingsId,
          birthdayRewardType: "AMOUNT_OFF",
        },
      });

      return {
        success: true,
        message: `Deleted ${deletedRewards.count} birthday rewards successfully`,
        count: deletedRewards.count,
        warnings: errors.length > 0 ? errors : undefined,
      };
    } catch (error) {
      console.error("Error deleting birthday reward:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to delete discount",
      };
    }
  } else {
    return {
      success: false,
      error: "Invalid request method",
    };
  }
};
