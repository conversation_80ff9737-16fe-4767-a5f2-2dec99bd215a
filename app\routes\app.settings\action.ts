import { LoaderFunctionArgs } from "@remix-run/node";
import { i18nextCookie } from "../../cookie.server";
import db from "../../db.server";
import { authenticate } from "../../shopify.server";

export const action = async ({ request }: LoaderFunctionArgs) => {
  const { admin } = await authenticate.admin(request);
  const formData = await request.formData();
  const action = formData.get("action");

  const response = await admin.graphql(`
    #graphql
      query GetShopDetails {
        shop {
          id
          name
        }
      }
    `);

  const responseJson = await response.json();

  const result = await db.shop.findFirst({
    where: {
      shopId: responseJson.data.shop.id,
    },
  });

  if (!result) {
    throw new Error("Shop not found");
  }

  // Handle different actions
  if (action === "generateApiKey") {
    const newKey =
      "sk_key_" +
      Math.random().toString(36).substring(2, 15) +
      Math.random().toString(36).substring(2, 15);

    await db.shop.update({
      where: {
        id: result.id,
      },
      data: {
        apiKey: newKey,
      },
    });

    return { apiKey: newKey };
  } else if (action === "updateLanguage") {
    const language = formData.get("language") as string;

    console.log("language: ", language);

    if (!language) {
      throw new Error("Language is required");
    }

    // Create headers with the cookie
    const headers = new Headers();
    headers.set("Set-Cookie", await i18nextCookie.serialize(language));

    // // Set the i18next cookie with the language value
    // // This cookie name 'i18next' is the default used by i18next-browser-languagedetector
    // headers.append("Set-Cookie", `i18next=${language}; Path=/; Max-Age=31536000; SameSite=Lax`);

    // Return a Response object with the cookie header
    return new Response(JSON.stringify({ language }), {
      headers,
      status: 200,
    });
  }

  throw new Error("Invalid action");
};

export default action;
