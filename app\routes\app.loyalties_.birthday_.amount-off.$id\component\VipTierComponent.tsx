// VipTierDiscounts.tsx
import { BlockStack, Button, Card, InlineStack, Text } from "@shopify/polaris";
import React from "react";
import { useTranslation } from "react-i18next";
import { TierDiscount, VIPTier } from "../types";

interface VipTierDiscountsProps {
  vipTiers: VIPTier[];
  tierDiscounts: TierDiscount[];
  onCreateDiscount: (tierId: number, tierName: string) => void;
  onEditDiscount: (tierDiscount: TierDiscount) => void;
}

export const VipTierDiscounts: React.FC<VipTierDiscountsProps> = ({
  vipTiers,
  tierDiscounts,
  onCreateDiscount,
  onEditDiscount,
}) => {
  const { t } = useTranslation();
  // Helper function to get discount info for a tier
  const getTierDiscountInfo = (tierId: number) => {
    return tierDiscounts.find((discount) => discount.tierId === tierId);
  };

  // Helper function to format discount display
  const formatDiscountDisplay = (discount: TierDiscount) => {
    const value =
      discount.discountType === "fixed" ? `$${discount.rewardValue}` : `${discount.rewardValue}%`;
    return `${value} off`;
  };

  return (
    <Card>
      <BlockStack gap="400">
        <Text as="h3" variant="headingSm" fontWeight="semibold">
          {t("birthday.amountOff.title")}
        </Text>

        {vipTiers.map((tier) => {
          const existingDiscount = getTierDiscountInfo(tier.id);
          const hasDiscount = existingDiscount?.hasDiscount || false;

          return (
            <div key={tier.id}>
              <div style={{ marginBottom: "0.5rem" }}>
                <Text as="h4" variant="headingXs" fontWeight="medium">
                  {tier.name}
                </Text>
              </div>
              <Card>
                <InlineStack align="space-between" blockAlign="center">
                  <BlockStack gap="100">
                    {hasDiscount && existingDiscount ? (
                      <>
                        <Text as="p" variant="bodyMd" fontWeight="medium">
                          {t("birthday.amountOff.order")}
                        </Text>
                        <Text as="p" variant="bodyMd" tone="subdued">
                          {formatDiscountDisplay(existingDiscount)}
                        </Text>
                      </>
                    ) : (
                      <Text as="p" variant="bodyMd" tone="subdued">
                        {t("birthday.noDiscount")}
                      </Text>
                    )}
                  </BlockStack>

                  <Button
                    variant={hasDiscount ? "plain" : "secondary"}
                    size="slim"
                    onClick={() => {
                      if (hasDiscount && existingDiscount) {
                        onEditDiscount(existingDiscount);
                      } else {
                        onCreateDiscount(tier.id, tier.name);
                      }
                    }}
                  >
                    {hasDiscount ? t("common.edit") : t("common.create")}
                  </Button>
                </InlineStack>
              </Card>
            </div>
          );
        })}
      </BlockStack>
    </Card>
  );
};
