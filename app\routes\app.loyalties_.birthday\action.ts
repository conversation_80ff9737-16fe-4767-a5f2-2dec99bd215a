import { LoaderFunctionArgs } from "@remix-run/node";
import db from "../../db.server";

export const action = async ({ request }: LoaderFunctionArgs) => {
  if (request.method === "POST") {
    const { settingsId, birthdayRewardType } = await request.json();
    // Create a new birthday reward in the local database
    const birthdayReward = await db.birthdayReward.create({
      data: {
        settingsId: Number(settingsId),
        birthdayRewardType: birthdayRewardType,
        title: "Birthday reward",
      },
      select: {
        id: true,
        birthdayRewardType: true,
      },
    });
    return birthdayReward;
  } else if (request.method === "PATCH") {
    const { id, pageTitle } = await request.json();
    const birthdayLoyaltyProgram = await db.birthdayProgramSettings.update({
      where: {
        id: id,
      },
      data: {
        pageTitle: pageTitle,
      },
      select: {
        id: true,
        pageTitle: true,
        isActive: true,
      },
    });
    return birthdayLoyaltyProgram;
  } else {
    throw new Error("Method not allowed");
  }
};
