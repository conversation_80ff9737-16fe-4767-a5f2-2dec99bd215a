import {
  CartInput,
  CartLinesDiscountsGenerateRunResult,
  OrderDiscountSelectionStrategy,
} from "../generated/api";

export function cartLinesDiscountsGenerateRun(
  input: CartInput,
): CartLinesDiscountsGenerateRunResult {
  const usingPoints = Number(input?.cart?.usingPoints?.value);
  const points = Number(input?.cart?.buyerIdentity?.customer?.metafield?.value);
  const discountValue = Number(input?.cart?.discountValue?.value);

  if (input?.cart?.buyerIdentity?.customer && usingPoints > 0 && usingPoints <= points)
    return {
      operations: [
        {
          orderDiscountsAdd: {
            candidates: [
              {
                message: `OMO_POINTS_${usingPoints} ${discountValue} ${input.cart.cost.totalAmount.currencyCode} off on your order`,
                targets: [
                  {
                    orderSubtotal: {
                      excludedCartLineIds: [],
                    },
                  },
                ],
                value: {
                  fixedAmount: {
                    amount: discountValue,
                  },
                },
              },
            ],
            selectionStrategy: OrderDiscountSelectionStrategy.First,
          },
        },
      ],
    };

  return {
    operations: [],
  };
}
