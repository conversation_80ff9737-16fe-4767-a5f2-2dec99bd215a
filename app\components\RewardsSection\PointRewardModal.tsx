import { Modal, TitleBar } from "@shopify/app-bridge-react";
import { BlockStack, Form, InlineStack, Text, TextField } from "@shopify/polaris";
import { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { MODAL_IDS, PointRewardInterface, REWARD_TYPES } from "./interface";

interface PointRewardModalProps {
  /**
   * Modal mode - "add" for creating new rewards, "edit" for updating existing ones
   */
  mode: "add" | "edit";

  /**
   * The reward to edit (required in edit mode)
   */
  reward?: PointRewardInterface;

  /**
   * Callback function when the reward is saved
   */
  onSave?: (reward: PointRewardInterface) => void;

  /**
   * Callback function when the modal is closed
   */
  onClose?: () => void;
}

export default function PointRewardModal({
  mode,
  reward,
  onSave,
  onClose,
}: Readonly<PointRewardModalProps>) {
  const { t } = useTranslation();

  // Initialize with empty values for add mode, or existing values for edit mode
  const [title, setTitle] = useState(mode === "edit" && reward ? reward.title : "");
  const [value, setValue] = useState(mode === "edit" && reward ? reward.value : "0");
  const [titleError, setTitleError] = useState("");
  const [valueError, setValueError] = useState("");

  // Update state when reward changes in edit mode
  useEffect(() => {
    if (mode === "edit" && reward) {
      setTitle(reward.title);
      setValue(reward.value);
      // Reset errors when reward changes
      setTitleError("");
      setValueError("");
    }
  }, [mode, reward]);

  const validateForm = useCallback(() => {
    let isValid = true;

    // Reset errors
    setTitleError("");
    setValueError("");

    // Validate title
    if (!title.trim()) {
      setTitleError(t("loyalties.rewards.validation.titleRequired"));
      isValid = false;
    }

    // Validate value
    if (!value.trim()) {
      setValueError(t("loyalties.rewards.validation.valueRequired"));
      isValid = false;
    } else if (isNaN(Number(value)) || Number(value) <= 0) {
      setValueError(t("loyalties.rewards.validation.valueInvalid"));
      isValid = false;
    }

    return isValid;
  }, [title, value, t]);

  const handleSave = useCallback(() => {
    if (validateForm() && onSave) {
      if (mode === "edit" && reward) {
        // Update existing reward
        const updatedReward: PointRewardInterface = {
          ...reward,
          title,
          value,
        };
        onSave(updatedReward);
      } else {
        // Create new reward
        const newReward: PointRewardInterface = {
          id: Date.now().toString(),
          type: REWARD_TYPES.POINTS,
          title,
          value,
        };
        onSave(newReward);
      }
    }
  }, [mode, onSave, reward, title, value, validateForm]);

  // Determine modal ID based on mode
  const modalId = mode === "edit" ? MODAL_IDS.EDIT_POINT_REWARD : MODAL_IDS.ADD_POINT_REWARD;

  // Determine modal title based on mode
  const modalTitle =
    mode === "edit" ? t("loyalties.rewards.editReward") : t("loyalties.rewards.addNewReward");

  // Determine heading based on mode
  const heading =
    mode === "edit" ? t("loyalties.rewards.editPointReward") : t("loyalties.rewards.pointReward");

  const handleSubmit = useCallback(() => {
    handleSave();
  }, [handleSave]);

  return (
    <Modal id={modalId}>
      <div className="m-3">
        <Form onSubmit={handleSubmit}>
          <BlockStack gap="400">
            <Text variant="headingMd" as="h2">
              {heading}
            </Text>
            <BlockStack>
              <Text variant="bodyMd" as="p">
                {t("loyalties.rewards.rewardTitle")}
              </Text>

              <TextField
                label={t("loyalties.rewards.rewardTitle")}
                value={title}
                type="text"
                onChange={setTitle}
                autoComplete="off"
                labelHidden
                error={titleError}
              />
            </BlockStack>

            <BlockStack>
              <Text variant="bodyMd" as="p">
                {t("loyalties.rewards.rewardValue")}
              </Text>
              <InlineStack gap="200" align="start" blockAlign="center">
                <TextField
                  label={t("loyalties.rewards.rewardValue")}
                  type="number"
                  value={value}
                  onChange={setValue}
                  autoComplete="off"
                  labelHidden
                  error={valueError}
                />

                <Text variant="bodyMd" as="p">
                  {t("loyalties.rewards.points")}
                </Text>
              </InlineStack>
            </BlockStack>
          </BlockStack>
        </Form>
      </div>
      <TitleBar title={modalTitle}>
        <button onClick={handleSave} variant={"primary"}>
          {t("common.save")}
        </button>
        <button onClick={onClose}>{t("common.cancel")}</button>
      </TitleBar>
    </Modal>
  );
}
