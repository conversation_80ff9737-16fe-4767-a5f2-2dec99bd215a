import { LoaderFunctionArgs } from "@remix-run/node";
import db from "../../db.server";
import { authenticate } from "../../shopify.server";
import { initMetafields } from "./service";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { admin, session } = await authenticate.admin(request);

  const url = new URL(request.url);

  const response = await admin.graphql(`
  #graphql
    query GetShopDetails {
      shop {
        id
        name
        myshopifyDomain
      }
    }
  `);

  const responseJson = await response.json();
  const result = await db.shop.findFirst({
    where: {
      shopId: responseJson.data.shop.id,
    },
  });

  await initMetafields(admin);

  if (!result) {
    await db.shop.create({
      data: {
        shopName: responseJson.data.shop.name ?? "",
        shopId: responseJson.data.shop.id ?? "",
        shopToken: session.accessToken,
        myshopifyDomain: responseJson.data.shop.myshopifyDomain,
      },
    });

    return {
      shopName: responseJson.data.shop.name ?? "",
      shopId: responseJson.data.shop.id ?? "",
      api<PERSON>ey: null,
      hostname: url.hostname,
    };
  }

  return {
    ...result,
    hostname: url.hostname,
  };
};

export default loader;
