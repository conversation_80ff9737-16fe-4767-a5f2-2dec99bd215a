import { Button, Checkbox, InlineStack } from "@shopify/polaris";
import React from "react";
import { useTranslation } from "react-i18next";
import IconText from "./IconText";

export interface StackItemProps {
  icon: React.ReactNode;
  title: string;
  subtitle?: string;
  active: boolean;
  /** Callback When toggle on/off */
  onToggle: (value: boolean) => void;
  /** Callback When Click Edit */
  onEdit: () => void;
}

const StackItem: React.FC<StackItemProps> = ({
  icon,
  title,
  subtitle,
  active,
  onToggle,
  onEdit,
}) => {
  const { t } = useTranslation();

  return (
    // Wrapper div for padding
    <div style={{ padding: "6px" }}>
      <InlineStack as="div" align="space-between" blockAlign="center" wrap={false}>
        {/* Icon + Title */}
        <IconText icon={icon} title={title} subtitle={subtitle} />
        {/* Checkbox + Edit */}
        <InlineStack as="div" align="start" blockAlign="center" gap="200" wrap={false}>
          <Checkbox label="" checked={active} onChange={onToggle} />
          <Button variant="plain" onClick={onEdit}>
            {t("common.edit")}
          </Button>
        </InlineStack>
      </InlineStack>
    </div>
  );
};

export default StackItem;
