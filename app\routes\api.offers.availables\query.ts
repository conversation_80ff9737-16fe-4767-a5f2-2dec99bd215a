import { gql } from "@apollo/client/core";

export const offersQuery = gql`
  query getAllDiscounts {
    discountNodes(first: 250, query: "status:active") {
      edges {
        node {
          id
          discount {
            __typename
            ... on DiscountCodeBasic {
              title
              summary
              codes(first: 10) {
                nodes {
                  code
                }
              }
              endsAt
              appliesOncePerCustomer
              discountClass
              customerGets {
                appliesOnOneTimePurchase
                appliesOnSubscription
                items {
                  ... on AllDiscountItems {
                    allItems
                  }
                  ... on DiscountCollections {
                    collections(first: 250) {
                      nodes {
                        id
                      }
                    }
                  }
                  ... on DiscountProducts {
                    products(first: 250) {
                      nodes {
                        id
                        title
                      }
                    }
                    productVariants(first: 250) {
                      nodes {
                        id
                        title
                        sku
                        product {
                          title
                        }
                      }
                    }
                  }
                }
                value {
                  ... on DiscountPercentage {
                    percentage
                  }
                  ... on DiscountAmount {
                    amount {
                      amount
                      currencyCode
                    }
                    appliesOnEachItem
                  }
                }
              }
              customerSelection {
                ... on DiscountCustomerAll {
                  allCustomers
                }
                ... on DiscountCustomerSegments {
                  segments {
                    id
                    name
                  }
                }
                ... on DiscountCustomers {
                  customers {
                    id
                  }
                }
              }
              minimumRequirement {
                ... on DiscountMinimumQuantity {
                  greaterThanOrEqualToQuantity
                }
                ... on DiscountMinimumSubtotal {
                  greaterThanOrEqualToSubtotal {
                    amount
                  }
                }
              }
              asyncUsageCount
            }
            ... on DiscountCodeBxgy {
              title
              endsAt
              discountClass
              codes(first: 250) {
                nodes {
                  code
                }
              }
              customerSelection {
                ... on DiscountCustomerAll {
                  allCustomers
                }
                ... on DiscountCustomerSegments {
                  segments {
                    id
                    name
                  }
                }
                ... on DiscountCustomers {
                  customers {
                    id
                  }
                }
              }
              customerGets {
                items {
                  ... on AllDiscountItems {
                    allItems
                  }
                  ... on DiscountCollections {
                    collections(first: 100) {
                      nodes {
                        id
                      }
                    }
                  }
                  ... on DiscountProducts {
                    products(first: 100) {
                      nodes {
                        id
                        title
                      }
                    }
                    productVariants(first: 100) {
                      nodes {
                        title
                        sku
                        product {
                          title
                        }
                      }
                    }
                  }
                }
                value {
                  __typename
                  ... on DiscountAmount {
                    amount {
                      amount
                    }
                  }
                  ... on DiscountOnQuantity {
                    quantity {
                      quantity
                    }
                    effect {
                      ... on DiscountAmount {
                        amount {
                          amount
                        }
                      }
                      ... on DiscountPercentage {
                        percentage
                      }
                    }
                  }
                }
              }
              customerBuys {
                value {
                  ... on DiscountQuantity {
                    quantity
                  }
                  ... on DiscountPurchaseAmount {
                    amount
                  }
                }
                items {
                  ... on AllDiscountItems {
                    allItems
                  }
                  ... on DiscountCollections {
                    collections(first: 100) {
                      edges {
                        node {
                          title
                          id
                        }
                      }
                    }
                  }
                  ... on DiscountProducts {
                    products(first: 100) {
                      edges {
                        node {
                          id
                          title
                        }
                      }
                    }
                    productVariants(first: 100) {
                      edges {
                        node {
                          title
                          sku
                          product {
                            title
                          }
                        }
                      }
                    }
                  }
                }
              }
              asyncUsageCount
            }
            ... on DiscountCodeFreeShipping {
              title
              endsAt
              discountClass
              codes(first: 10) {
                nodes {
                  code
                }
              }
              customerSelection {
                ... on DiscountCustomerAll {
                  allCustomers
                }
                ... on DiscountCustomerSegments {
                  segments {
                    id
                    name
                  }
                }
                ... on DiscountCustomers {
                  customers {
                    id
                  }
                }
              }
              minimumRequirement {
                ... on DiscountMinimumQuantity {
                  greaterThanOrEqualToQuantity
                }
                ... on DiscountMinimumSubtotal {
                  greaterThanOrEqualToSubtotal {
                    amount
                  }
                }
              }
              asyncUsageCount
            }
            ... on DiscountAutomaticApp {
              title
              endsAt
              discountClass
              asyncUsageCount
            }
            ... on DiscountAutomaticBxgy {
              title
              endsAt
              discountClass
              customerGets {
                items {
                  ... on AllDiscountItems {
                    allItems
                  }
                  ... on DiscountCollections {
                    collections(first: 250) {
                      nodes {
                        title
                        id
                      }
                    }
                  }
                  ... on DiscountProducts {
                    products(first: 250) {
                      nodes {
                        id
                        title
                      }
                    }
                    productVariants(first: 250) {
                      nodes {
                        title
                        sku
                        product {
                          title
                        }
                      }
                    }
                  }
                }
                value {
                  __typename
                  ... on DiscountAmount {
                    amount {
                      amount
                    }
                  }
                  ... on DiscountOnQuantity {
                    quantity {
                      quantity
                    }
                    effect {
                      ... on DiscountAmount {
                        amount {
                          amount
                        }
                      }
                      ... on DiscountPercentage {
                        percentage
                      }
                    }
                  }
                }
              }
              customerBuys {
                items {
                  ... on AllDiscountItems {
                    allItems
                  }
                  ... on DiscountCollections {
                    collections(first: 250) {
                      edges {
                        node {
                          title
                          id
                        }
                      }
                    }
                  }
                  ... on DiscountProducts {
                    products(first: 250) {
                      edges {
                        node {
                          id
                          title
                        }
                      }
                    }
                    productVariants(first: 250) {
                      edges {
                        node {
                          title
                          sku
                          product {
                            title
                          }
                        }
                      }
                    }
                  }
                }
                value {
                  ... on DiscountQuantity {
                    quantity
                  }
                  ... on DiscountPurchaseAmount {
                    amount
                  }
                }
              }
              asyncUsageCount
            }
            ... on DiscountAutomaticFreeShipping {
              title
              endsAt
              discountClass
              minimumRequirement {
                ... on DiscountMinimumQuantity {
                  greaterThanOrEqualToQuantity
                }
                ... on DiscountMinimumSubtotal {
                  greaterThanOrEqualToSubtotal {
                    amount
                  }
                }
              }
              asyncUsageCount
            }
            ... on DiscountAutomaticBasic {
              title
              endsAt
              discountClass
              customerGets {
                appliesOnOneTimePurchase
                appliesOnSubscription
                items {
                  ... on AllDiscountItems {
                    allItems
                  }
                  ... on DiscountCollections {
                    collections(first: 250) {
                      nodes {
                        id
                      }
                    }
                  }
                  ... on DiscountProducts {
                    products(first: 250) {
                      nodes {
                        id
                        title
                      }
                    }
                    productVariants(first: 250) {
                      nodes {
                        id
                        title
                        sku
                        product {
                          title
                        }
                      }
                    }
                  }
                }
                value {
                  ... on DiscountPercentage {
                    percentage
                  }
                  ... on DiscountAmount {
                    amount {
                      amount
                      currencyCode
                    }
                    appliesOnEachItem
                  }
                }
              }
              minimumRequirement {
                ... on DiscountMinimumQuantity {
                  greaterThanOrEqualToQuantity
                }
                ... on DiscountMinimumSubtotal {
                  greaterThanOrEqualToSubtotal {
                    amount
                  }
                }
              }
              asyncUsageCount
            }
          }
        }
      }
      pageInfo {
        hasNextPage
        endCursor
      }
    }
  }
`;

export const segmentsQuery = gql`
  query {
    segments(first: 250) {
      nodes {
        id
      }
    }
  }
`;

export const customerSegmentMembershipQuery = gql`
  query customerSegmentMembership($customerId: ID!, $segmentIds: [ID!]!) {
    customerSegmentMembership(customerId: $customerId, segmentIds: $segmentIds) {
      memberships {
        isMember
        segmentId
      }
    }
  }
`;

export const collectionsQuery = gql`
  query collections($ids: String!, $first: Int!) {
    collections(first: $first, query: $ids) {
      nodes {
        id
        title
        products(first: 100) {
          nodes {
            id
            title
            variants(first: 50) {
              nodes {
                title
                sku
              }
            }
          }
        }
      }
    }
  }
`;

export const productsQuery = gql`
  query products($ids: String!, $first: Int!) {
    products(first: $first, query: $ids) {
      nodes {
        id
        title
        variants(first: 250) {
          nodes {
            title
            sku
          }
        }
      }
    }
  }
`;
