import { Modal, TitleBar } from "@shopify/app-bridge-react";
import { BlockStack, Form, InlineStack, Text, TextField } from "@shopify/polaris";
import { useCallback, useState } from "react";
import { MODAL_IDS, PointRewardInterface, REWARD_TYPES } from "../interface";

interface AddPointRewardModalProps {
  onSave?: (reward: PointRewardInterface) => void;
  onClose?: () => void;
}

export default function AddPointRewardModal({
  onSave,
  onClose,
}: Readonly<AddPointRewardModalProps>) {
  const [title, setTitle] = useState("");
  const [value, setValue] = useState("0");
  const [titleError, setTitleError] = useState("");
  const [valueError, setValueError] = useState("");

  const validateForm = useCallback(() => {
    let isValid = true;

    // Reset errors
    setTitleError("");
    setValueError("");

    // Validate title
    if (!title.trim()) {
      setTitleError("Reward title is required");
      isValid = false;
    }

    // Validate value
    if (!value.trim()) {
      setValueError("Reward value is required");
      isValid = false;
    } else if (isNaN(Number(value)) || Number(value) <= 0) {
      setValueError("Reward value must be a positive number");
      isValid = false;
    }

    return isValid;
  }, [title, value]);

  const handleSave = useCallback(() => {
    if (validateForm() && onSave) {
      const reward: PointRewardInterface = {
        id: Date.now().toString(),
        type: REWARD_TYPES.POINTS,
        title,
        value,
      };
      onSave(reward);
    }
  }, [onSave, title, value, validateForm]);

  const handleSubmit = useCallback(() => {
    handleSave();
  }, [handleSave]);

  return (
    <Modal id={MODAL_IDS.ADD_POINT_REWARD}>
      <div className="m-3">
        <Form onSubmit={handleSubmit}>
          <BlockStack gap="400">
            <Text variant="headingMd" as="h2">
              Point reward
            </Text>
            <BlockStack>
              <Text variant="bodyMd" as="p">
                Reward Title
              </Text>

              <TextField
                label="Reward Title"
                value={title}
                type="text"
                onChange={setTitle}
                autoComplete="off"
                labelHidden
                error={titleError}
              />
            </BlockStack>

            <BlockStack>
              <Text variant="bodyMd" as="p">
                Reward Value
              </Text>
              <InlineStack gap="200" align="start" blockAlign="center">
                <TextField
                  label="Reward Value"
                  type="number"
                  value={value}
                  onChange={setValue}
                  autoComplete="off"
                  labelHidden
                  error={valueError}
                />

                <Text variant="bodyMd" as="p">
                  Points
                </Text>
              </InlineStack>
            </BlockStack>
          </BlockStack>
        </Form>
      </div>
      <TitleBar title="Add New Reward">
        <button onClick={handleSave} variant={"primary"}>
          Save
        </button>
        <button onClick={onClose}>Cancel</button>
      </TitleBar>
    </Modal>
  );
}
