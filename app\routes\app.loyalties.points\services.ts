import db from "@/db.server";
import { LoyaltyProgramType } from "@prisma/client";
import dayjs from "dayjs";

/**
 * Fetches points settings for a shop
 */
export async function getPointsSettings(shopDomain: string) {
  if (!shopDomain) return;

  const shop = await db.shop.findFirst({
    where: {
      myshopifyDomain: shopDomain,
    },
  });

  const loyaltyProgram = await db.loyaltyProgram.findFirst({
    where: {
      shopId: shop?.id,
      programType: LoyaltyProgramType.POINTS,
    },
    include: {
      points: true,
    },
  });

  // Fetch Points Settings if the program exists
  const pointsSettings = await db.loyaltyPoints.findMany({
    where: {
      loyaltyProgramId: loyaltyProgram?.id,
    },
  });

  return { pointsSettings };
}

/**
 * Fetches VIP tier settings for a shop
 */
export async function getPointsAccoringVipSettings(shopDomain: string) {
  if (!shopDomain) return;

  const shop = await db.shop.findFirst({
    where: {
      myshopifyDomain: shopDomain,
    },
  });

  const loyaltyProgram = await db.loyaltyProgram.findFirst({
    where: {
      shopId: shop?.id,
      programType: LoyaltyProgramType.VIP_TIER,
    },
    include: {
      vipTiers: true,
    },
  });

  // Fetch Points Settings According to the Vip Tier
  const vipTiersSettings = loyaltyProgram?.vipTiers;

  return { vipTiersSettings };
}

/**
 * Get point order settings for a shop
 */
export async function getPointOrderSetting(shopDomain: string) {
  if (!shopDomain) return;

  // Get the shop information
  const shop = await db.shop.findFirst({
    where: {
      myshopifyDomain: shopDomain,
    },
    include: {
      loyaltyPrograms: {
        where: {
          programType: LoyaltyProgramType.POINTS,
        },
        include: {
          points: true,
        },
      },
    },
  });

  if (!shop) {
    throw new Error(`Shop not found for domain ${shopDomain}`);
  }

  const loyaltyProgram = shop.loyaltyPrograms[0];
  const loyaltyPoints = loyaltyProgram?.points;

  return { loyaltyPoints };
}

/**
 * Save delayed points to database for cron job processing
 * This is the main function used by webhooks
 */
export async function saveDelayedPoints(params: {
  customerGid: string;
  orderId: string;
  points: number;
  issueAt: Date;
}) {
  const { customerGid, orderId, points, issueAt } = params;

  try {
    const record = await db.loyaltyPointsHistory.create({
      data: {
        customerId: customerGid,
        orderId: orderId.toString(),
        points: points,
        earnedAt: new Date(), // When the order was created
        issueAt: issueAt, // When points should be issued (after X days)
        status: "PENDING", // Will be processed by cron job
        createdAt: new Date(),
      },
    });

    return record;
  } catch (error) {
    console.error("Error saving delayed points:", error);
    throw error;
  }
}

/**
 * Get customer points history (for admin panel)
 */
export async function getCustomerPointsHistory(customerId: string) {
  return db.loyaltyPointsHistory.findMany({
    where: {
      customerId,
    },
    orderBy: {
      earnedAt: "desc",
    },
  });
}

/**
 * Get delayed points statistics (for monitoring)
 */
export async function getDelayedPointsStats() {
  const stats = await db.loyaltyPointsHistory.groupBy({
    by: ["status"],
    _count: {
      id: true,
    },
    _sum: {
      points: true,
    },
  });

  const totalRecords = await db.loyaltyPointsHistory.count();

  // Get counts for each status
  const pending = stats.find((s) => s.status === "PENDING")?._count.id || 0;
  const issued = stats.find((s) => s.status === "ISSUED")?._count.id || 0;
  const error = stats.find((s) => s.status === "ERROR")?._count.id || 0;

  // Get overdue count
  const overdue = await db.loyaltyPointsHistory.count({
    where: {
      status: "PENDING",
      issueAt: {
        lt: new Date(),
      },
    },
  });

  return {
    byStatus: stats,
    totalRecords,
    counts: {
      pending,
      issued,
      error,
      overdue,
    },
    timestamp: new Date().toISOString(),
  };
}

/**
 * Utility function to check if delayed points are enabled for a shop
 */
export async function isDelayedPointsEnabled(shopDomain: string): Promise<boolean> {
  try {
    const pointOrderSettings = await getPointOrderSetting(shopDomain);
    const loyaltyPoints = pointOrderSettings?.loyaltyPoints;

    return loyaltyPoints?.pointsIssueType === "DELAYED" && (loyaltyPoints?.issueDays || 0) > 0;
  } catch (error) {
    console.error("Error checking delayed points settings:", error);
    return false;
  }
}

/**
 * Get next issue date based on settings
 */
export async function getNextIssueDate(
  shopDomain: string,
  orderDate: Date = new Date(),
): Promise<Date> {
  const pointOrderSettings = await getPointOrderSetting(shopDomain);
  const issueDays = pointOrderSettings?.loyaltyPoints?.issueDays || 0;

  const issueDate = new Date(orderDate);
  issueDate.setDate(issueDate.getDate() + issueDays);

  return issueDate;
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export async function createPointsDiscount(admin: any) {
  const discountExistsResponse = await admin.graphql(
    `#graphql
    {
      discountNodes(
        query: "method:automatic AND status:active AND type:app"
        first: 50
      ) {
        edges {
          node {
            id
            discount {
              ... on DiscountAutomaticApp {
                __typename
                appDiscountType {
                  functionId
                }
              }
            }
          }
        }
      }
    }
    `,
  );
  const discountExistsResponseJson = await discountExistsResponse.json();

  const response = await admin.graphql(
    `#graphql
    {
      app {
        id
      }
      shopifyFunctions(first: 50, apiType: "discount") {
        nodes {
          id
          app {
            title
            id
          }
        }
      }
    }`,
  );
  const responseJson = await response.json();
  const functions = responseJson.data.shopifyFunctions.nodes;
  const app = responseJson.data.app.id;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const functionId = functions.find((f: any) => f.app.id === app)?.id;
  if (!functionId) return;
  if (
    discountExistsResponseJson.data.discountNodes.edges.find(
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (item: any) => item.node.discount?.appDiscountType?.functionId === functionId,
    )
  )
    return;

  const discountResponse = await admin.graphql(
    `#graphql
    mutation discountAutomaticAppCreate($automaticAppDiscount: DiscountAutomaticAppInput!) {
      discountAutomaticAppCreate(automaticAppDiscount: $automaticAppDiscount) {
        userErrors {
          code
          field
        }
      }
    }`,
    {
      variables: {
        automaticAppDiscount: {
          title: "**DO NOT DELETE** Discount for OMO redeem points",
          functionId: functionId,
          combinesWith: {
            productDiscounts: true,
            orderDiscounts: true,
            shippingDiscounts: true,
          },
          startsAt: dayjs().toISOString(),
          discountClasses: "ORDER",
        },
      },
    },
  );

  const discountJson = await discountResponse.json();
  return discountJson.data.discountAutomaticAppCreate.userErrors.length === 0;
}
