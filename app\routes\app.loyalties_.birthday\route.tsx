import { useLoaderData, useNavigate } from "@remix-run/react";
import { useAppBridge } from "@shopify/app-bridge-react";
import {
  Banner,
  BlockStack,
  Box,
  Button,
  Card,
  Icon,
  InlineStack,
  Layout,
  List,
  Page,
  Text,
  TextField,
} from "@shopify/polaris";
import { InfoIcon, PlusIcon } from "@shopify/polaris-icons";
import { useCallback, useState } from "react";
import { useTranslation } from "react-i18next";
import { action } from "./action";
import AddRewardModal from "./add_new_reward_modal";
import { loader } from "./loader";
import { rewardOptions } from "./reward_options";

// Export the action and loader functions
export { action, loader };

export default function BirthdayRewards() {
  const data = useLoaderData<typeof loader>();

  const [title, setTitle] = useState(data.pageTitle);
  const shopify = useAppBridge();
  const navigate = useNavigate();
  const { t } = useTranslation();

  const handleAddRewardModalOpen = useCallback(() => {
    shopify?.modal?.show("add-reward-modal");
  }, [shopify]);

  const handleAddRewardModalClose = useCallback(() => {
    shopify?.modal?.hide("store-credit-edit-modal");
  }, [shopify]);

  const handleSavePageTitle = useCallback(async () => {
    await fetch("/app/loyalties/birthday", {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        id: data.id,
        pageTitle: title,
      }),
    });
  }, [data.id, title]);

  return (
    <Page title={t("birthday.title")}>
      <Layout>
        <Layout.Section>
          <Card>
            <TextField
              label={t("birthday.form.titleLabel")}
              placeholder={t("birthday.form.titlePlaceholder")}
              autoComplete="off"
              value={title}
              onChange={setTitle}
            />
            <Text as="p" tone="subdued">
              {t("birthday.form.titleDescription")}
            </Text>
          </Card>
        </Layout.Section>

        <Layout.Section>
          <Card>
            <InlineStack align="space-between" gap="200">
              <BlockStack>
                <Text variant="headingMd" as="h2">
                  {t("birthday.rewards.title")}
                </Text>
                {/* <Text as="p" tone="subdued">{t("birthday.rewards.maxLimit")}</Text> */}
              </BlockStack>
              <BlockStack align="start" gap="300">
                <Button variant="primary" icon={PlusIcon} onClick={handleAddRewardModalOpen}>
                  {t("birthday.rewards.addNew")}
                </Button>
              </BlockStack>
            </InlineStack>

            <BlockStack>
              {data.rewards.length > 0 ? (
                data.rewards.map((reward) => {
                  const rewardOption = rewardOptions.find(
                    (option) => option.id === reward.birthdayRewardType,
                  );
                  return (
                    <div
                      style={{
                        padding: "16px 20px",
                        marginTop: "0.1rem",
                        marginBottom: "0.1rem",
                      }}
                      key={reward.id}
                    >
                      <InlineStack align="space-between" blockAlign="center" key={reward.id}>
                        <BlockStack gap="200">
                          <InlineStack gap="200" align="start">
                            <Icon source={rewardOption?.icon ?? "placeholder"} />
                            <BlockStack gap="100">
                              <Text as="h2" variant="headingMd">
                                {reward.title || rewardOption?.title}
                              </Text>
                              <Text as="h3">
                                {reward.discountType === "PERCENTAGE"
                                  ? `${reward.rewardValue}%`
                                  : `$${reward.rewardValue}`}{" "}
                                {t("birthday.amountOff.off")}
                              </Text>
                            </BlockStack>
                          </InlineStack>
                        </BlockStack>
                        <BlockStack gap="200">
                          <Button
                            variant="plain"
                            onClick={() => navigate(`${rewardOption?.link}/${reward.id}`)}
                          >
                            {t("common.edit")}
                          </Button>
                        </BlockStack>
                      </InlineStack>
                    </div>
                  );
                })
              ) : (
                <Banner tone="warning" icon={InfoIcon}>
                  {t("birthday.rewards.addRewardWarning")}
                </Banner>
              )}
              <Banner tone="info" icon={InfoIcon}>
                {t("birthday.rewards.displayInfo")}
              </Banner>
            </BlockStack>
          </Card>
        </Layout.Section>

        <Layout.Section>
          <Card>
            <BlockStack gap={"200"}>
              <Text variant="headingMd" as="h2">
                {t("birthday.birthdaySection.title")}
              </Text>
              <Text as="p">{t("birthday.birthdaySection.editableInfo")}</Text>
              <Text variant="headingMd" as="h2">
                {t("birthday.genderOptions.title")}
              </Text>
              <List gap="loose">
                <List.Item>{t("birthday.genderOptions.female")}</List.Item>
                <List.Item>{t("birthday.genderOptions.male")}</List.Item>
                <List.Item>{t("birthday.genderOptions.transgender")}</List.Item>
                <List.Item>{t("birthday.genderOptions.nonBinary")}</List.Item>
                <List.Item>{t("birthday.genderOptions.preferNotToSay")}</List.Item>
              </List>
            </BlockStack>

            <BlockStack inlineAlign="start" gap="200">
              <Button>{t("birthday.genderOptions.editButton")}</Button>
            </BlockStack>

            <Box paddingBlockEnd="600" />

            <Banner tone="info" icon={InfoIcon}>
              {t("birthday.eligibilityInfo")}
            </Banner>
          </Card>
        </Layout.Section>

        <Layout.Section>
          <BlockStack align="center" inlineAlign="end" gap="200">
            <Button size="large" variant="primary" onClick={handleSavePageTitle}>
              {t("common.save")}
            </Button>
          </BlockStack>
        </Layout.Section>
      </Layout>

      <AddRewardModal
        id="add-reward-modal"
        settingsId={data.id}
        onHide={handleAddRewardModalClose}
        navigate={navigate}
      />
    </Page>
  );
}
