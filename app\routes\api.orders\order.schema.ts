import Joi from "joi";
import { CreateDraftOrderMutationInput, CreateDraftOrderMutationResponse, RefundCreateMutationInput, RefundCreateMutationResponse } from "./mutation";
import { QueryOrderWithSuggestedRefundResponse } from './query';

export interface IOrder {
  cellphone?: string;
  orderId: string;
  purchaseType: "Sales" | "Return";
  orderDate: string;
  products: Array<{
    qty: number;
    productSku: string;
    productName?: string;
    unitPrice?: number;
  }>;
  totalOrderAmount?: number;
  discountCodes?: string[];
  loyaltyPoint?: number;
  loyaltyPointDiscountAmount?: number;
  discountAmount?: number;
  actualOrderAmount: number;
  locationID: string;
  staffID: string;
  invoice?: string;
  relativeOrderId?: string;
}

export const orderSchema = Joi.object<IOrder>({
  cellphone: Joi.string()
    .pattern(/^09\d{8}$/)
    .optional()
    // .required()
    .messages({
      "string.pattern.base": "The 'cellphone' field must be a valid phone number",
    }),
  orderId: Joi.string().required().messages({
    "any.required": "The 'orderId' field is required",
    "string.empty": "The 'orderId' field cannot be empty",
  }),
  purchaseType: Joi.string().valid("Sales", "Return").required().messages({
    "any.required": "The 'purchaseType' field is required",
    "any.only": "The 'purchaseType' must be either 'Sales' or 'Return'",
  }),
  orderDate: Joi.string()
    .pattern(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\+\d{2}:\d{2})?$/)
    .required()
    .messages({
      "any.required": "The 'orderDate' field is required",
      "date.format":
        "The 'orderDate' field must be in 'YYYY-MM-DDThh:mm:ss' format, optionally followed by a timezone offset like '+08:00'",
    }),
  products: Joi.array()
    .items(
      Joi.object({
        qty: Joi.number().integer().positive().required().messages({
          "any.required": "The 'qty' field is required",
          "number.base": "The 'qty' field must be a number",
          "number.positive": "The 'qty' field must be a positive number",
        }),
        productSku: Joi.string().required().messages({
          "any.required": "The 'productSku' field is required",
          "string.empty": "The 'productSku' field cannot be empty",
        }),
        productName: Joi.string().required().optional(),
        unitPrice: Joi.number().positive().optional(),
      }),
    )
    .required()
    .messages({
      "any.required": "The 'products' field is required",
    }),
  totalOrderAmount: Joi.when('purchaseType', {
    is: 'Sales',
    then: Joi.number().required().messages({
      "any.required": "The 'totalOrderAmount' field is required for Sales orders",
      "number.base": "The 'totalOrderAmount' field must be a number",
      "number.positive": "The 'totalOrderAmount' field must be a positive number",
    }),
    otherwise: Joi.forbidden().messages({
      "any.unknown": "The 'actualOrderAmount' field must not be defined for Sales orders",
    })
  }),
  discountCodes: Joi.when('purchaseType', {
    is: 'Sales',
    then: Joi.array().items(Joi.string()).optional().messages({
      "array.base": "The 'discountCodes' field must be an array of strings",
      "string.base": "Each 'discountCode' must be a string",
      "string.empty": "Each 'discountCode' cannot be empty",
    }),
    otherwise: Joi.forbidden().messages({
      "any.unknown": "The 'actualOrderAmount' field must not be defined for Sales orders",
    }),
  }),
  loyaltyPoint: Joi.number().integer().optional(),
  loyaltyPointDiscountAmount: Joi.number().optional(),
  discountAmount: Joi.when('purchaseType', {
    is: 'Sales',
    then: Joi.number().required().messages({
      "any.required": "The 'discountAmount' field is required for Sales orders",
      "number.base": "The 'discountAmount' field must be a number",
      "number.positive": "The 'discountAmount' field must be a positive number",
    }),
    otherwise: Joi.forbidden().messages({
      "any.unknown": "The 'discountAmount' field must not be defined for Sales orders",
    })
  }),
  actualOrderAmount: Joi.number().required().messages({
    "any.required": "The 'actualOrderAmount' field is required",
    "number.base": "The 'actualOrderAmount' field must be a number",
    "number.positive": "The 'actualOrderAmount' field must be a positive number",
  }),
  locationID: Joi.string().required().messages({
    "any.required": "The 'locationID' field is required",
    "string.empty": "The 'locationID' field cannot be empty",
  }),
  staffID: Joi.string().required().messages({
    "any.required": "The 'staffID' field is required",
    "string.empty": "The 'staffID' field cannot be empty",
  }),
  invoice: Joi.string().optional(),
  relativeOrderId: Joi.when('purchaseType', {
    is: 'Sales',
    then: Joi.forbidden().messages({
      "any.unknown": "The 'relativeOrderId' field must not be defined for Sales orders",
    }),
    otherwise: Joi.string().required().messages({
      "any.required": "The 'relativeOrderId' field is required for Return orders",
      "string.empty": "The 'relativeOrderId' field cannot be empty",
    }),
  }),
});

export const orderMetafields = (
  orderInput: IOrder
): {
  key: string;
  type: string;
  value: string;
}[] => [
  {
    key: "order_id",
    type: "single_line_text_field",
    value: orderInput.orderId,
  },
  {
    key: "order_date",
    type: "date_time",
    value: orderInput.orderDate,
  },
  ...(orderInput.invoice
    ? [
        {
          key: "invoice",
          type: "single_line_text_field",
          value: orderInput.invoice,
        },
      ]
    : []),
  // ...(orderInput.totalOrderAmount
  //   ? [
  //       {
  //         key: "total_order_amount",
  //         type: "number_decimal",
  //         value: String(orderInput.totalOrderAmount),
  //       },
  //     ]
  //   : []
  // ),
  // ...(orderInput.discountAmount
  //   ? [
  //       {
  //         key: "discount_amount",
  //         type: "number_decimal",
  //         value: String(orderInput.discountAmount),
  //       },
  //     ]
  //   : []),
  // ...(orderInput.actualOrderAmount
  //   ? [
  //       {
  //         key: "actual_order_amount",
  //         type: "number_decimal",
  //         value: String(orderInput.actualOrderAmount),
  //       },
  //     ]
  //   : []),
  {
    key: "location_id",
    type: "single_line_text_field",
    value: orderInput.locationID,
  },
  {
    key: "staff_id",
    type: "single_line_text_field",
    value: orderInput.staffID,
  },
]

export const transformOrderToCreateDraftInput = (
  orderInput: IOrder,
  memberId: string | undefined,
  products: Array<{
    variantId?: string;
    productId?: string;
    productName?: string;
    unitPrice?: number;
    qty: number;
    productSku: string;
  }>,
): CreateDraftOrderMutationInput => ({
  input: {
    purchasingEntity: memberId ? {
      customerId: memberId,
    } : undefined,
    taxExempt: true,
    lineItems: products.map((product) => ({
      title: product.productName,
      sku: product.productSku,
      variantId: product.variantId,
      originalUnitPriceWithCurrency: product.unitPrice
        ? {
            amount: product.unitPrice,
            currencyCode: "TWD",
          }
        : undefined,
      priceOverride: product.unitPrice
        ? {
            amount: product.unitPrice,
            currencyCode: "TWD",
          }
        : undefined,
      quantity: product.qty,
    })),
    discountCodes: orderInput.discountCodes,
    metafields: orderMetafields(orderInput),
  },
});

export const transformOrderFromCreateDraftResponse = (response: CreateDraftOrderMutationResponse) => {
  const draftOrder = response.draftOrderCreate.draftOrder;
  return {
    cellphone: draftOrder.customer?.phone,
    purchaseType: "Sales",
    orderId: draftOrder.metafields.nodes.find(
      (metafield: { key: string; value: string }) => metafield.key === "order_id",
    )?.value,
    // shopifyOrderId: order.id,
    orderDate: draftOrder.metafields.nodes.find((metafield) => metafield.key === "order_date")
      ?.value,
    products: draftOrder.lineItems.nodes.map((lineItem) => ({
      qty: lineItem.quantity,
      productSku: lineItem.sku,
      productName: lineItem.title,
      unitPrice: Number(lineItem.originalUnitPriceSet.shopMoney.amount),
    })),
    totalOrderAmount: Number(draftOrder.totalLineItemsPriceSet.shopMoney.amount),
    discountCodes: draftOrder.discountCodes,
    loyaltyPoint: undefined, //TODO: get from CRM
    loyaltyPointDiscountAmount: undefined, //TODO: get from CRM
    discountAmount: Number(draftOrder.totalDiscountsSet.shopMoney.amount),
    actualOrderAmount: Number(draftOrder.totalPriceSet.shopMoney.amount),
    locationID: draftOrder.metafields.nodes.find(
      (metafield: { key: string; value: string }) => metafield.key === "location_id",
    )?.value,
    staffID: draftOrder.metafields.nodes.find(
      (metafield: { key: string; value: string }) => metafield.key === "staff_id",
    )?.value,
    invoice: draftOrder.metafields.nodes.find(
      (metafield: { key: string; value: string }) => metafield.key === "invoice",
    )?.value,
    relativeOrderId: undefined,
  };
};

export const transformRelativeOrderToRefundInput = (
  relativeOrder: QueryOrderWithSuggestedRefundResponse["order"],
): RefundCreateMutationInput => ({
  input: {
    orderId: relativeOrder.id,
    refundLineItems: relativeOrder.suggestedRefund.refundLineItems.map((refundLineItem) => ({
      lineItemId: refundLineItem.lineItem.id,
      quantity: refundLineItem.quantity,
    })),
    transactions: [
      {
        amount: Number(relativeOrder.suggestedRefund.amountSet.shopMoney.amount),
        gateway: "cash",
        kind: "REFUND",
        orderId: relativeOrder.id,
      }
    ],
  }
})

export const transformRefundCreateOutputToResponse = (
  refundCreate: RefundCreateMutationResponse["refundCreate"],
  orderId: string,
) => ({
  cellphone: refundCreate.order.metafields.nodes.find(
    (metafield: { key: string; value: string }) => metafield.key === "cellphone",
  )?.value,
  purchaseType: "Return",
  orderId: orderId,
  orderDate: refundCreate.order.metafields.nodes.find((metafield) => metafield.key === "order_date")
    ?.value,
  products: refundCreate.refund.refundLineItems.nodes.map((lineItem) => ({
    qty: lineItem.quantity,
    productSku: lineItem.lineItem.sku,
    productName: lineItem.lineItem.name,
    unitPrice: Number(lineItem.priceSet.shopMoney.amount),
  })),
  totalOrderAmount: undefined,
  discountCodes: undefined, // Assuming discount codes are not available in refund response
  loyaltyPoint: undefined, //TODO: get from CRM
  loyaltyPointDiscountAmount: undefined, //TODO: get from CRM
  discountAmount: undefined, // Assuming discount amount is not available in refund response
  actualOrderAmount: -Number(refundCreate.refund.totalRefundedSet.shopMoney.amount),
  locationID: refundCreate.order.metafields.nodes.find(
    (metafield: { key: string; value: string }) => metafield.key === "location_id",
  )?.value,
  staffID: refundCreate.order.metafields.nodes.find(
    (metafield: { key: string; value: string }) => metafield.key === "staff_id",
  )?.value,
  invoice: refundCreate.order.metafields.nodes.find(
    (metafield: { key: string; value: string }) => metafield.key === "invoice",
  )?.value,
  relativeOrderId: refundCreate.order.metafields.nodes.find(
    (metafield: { key: string; value: string }) => metafield.key === "order_id",
  )?.value,
});