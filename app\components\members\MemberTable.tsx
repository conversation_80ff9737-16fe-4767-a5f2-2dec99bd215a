// File: app/components/members/MemberTable.tsx
import React from "react";
import { Link } from "@remix-run/react";
import { IndexTable, Text, Badge, Box, Avatar, EmptySearchResult, Spinner } from "@shopify/polaris";
import { Member } from "../../types/memberTypes";
import {
  MEMBER_RESOURCE_NAME,
  MEMBER_TABLE_HEADINGS,
  MemberVipTier,
} from "../../routes/app.members/constants";

interface MemberTableProps {
  customers: Member[];
  isLoading: boolean;
  emptyStateMarkup?: React.ReactNode;
}

export function MemberTable({
  customers,
  isLoading,
  emptyStateMarkup = (
    <EmptySearchResult
      title="No members found"
      description="Try changing filters or search keywords"
      withIllustration
    />
  ),
}: MemberTableProps) {
  return (
    <div style={{ position: "relative" }}>
      {isLoading && (
        <div className="absolute inset-0 bg-white/50 flex justify-center items-center z-10">
          <Spinner size="large" />
        </div>
      )}

      <IndexTable
        resourceName={MEMBER_RESOURCE_NAME}
        itemCount={customers.length}
        selectable={false}
        headings={MEMBER_TABLE_HEADINGS}
        emptyState={emptyStateMarkup}
      >
        {customers.map((customer: Member, index: number) => (
          <MemberTableRow customer={customer} index={index} key={customer.id} />
        ))}
      </IndexTable>
    </div>
  );
}

interface MemberTableRowProps {
  customer: Member;
  index: number;
}

function MemberTableRow({ customer, index }: MemberTableRowProps) {
  return (
    <IndexTable.Row id={customer.id} position={index}>
      {/* Name Cell */}
      <IndexTable.Cell>
        <Box>
          <div style={{ display: "flex", alignItems: "center", gap: "12px" }}>
            <Avatar customer size="md" name={customer.fullName.charAt(0)} />
            <div>
              <Link
                to={`/app/members/${customer.numericId}`}
                prefetch="intent"
                style={{ textDecoration: "none" }}
              >
                <Text variant="bodyMd" fontWeight="bold" as="span">
                  {customer.fullName}
                </Text>
              </Link>

              {customer.email && (
                <Text variant="bodySm" as="p" tone="subdued">
                  {customer.email}
                </Text>
              )}
            </div>
          </div>
        </Box>
      </IndexTable.Cell>

      {/* Points Cell */}
      <IndexTable.Cell>
        <Text
          variant="bodyMd"
          tone={customer.points && Number(customer.points) > 0 ? "success" : "subdued"}
          as="span"
        >
          {customer.points && Number(customer.points) > 0 ? customer.points : "No points"}
        </Text>
      </IndexTable.Cell>

      {/* VIP Tier Cell */}
      <IndexTable.Cell>
        {customer.vipTier ? (
          <Badge
            tone={
              customer.vipTier === MemberVipTier.SSVIP
                ? "success"
                : customer.vipTier === MemberVipTier.SVIP
                  ? "info"
                  : customer.vipTier === MemberVipTier.VIP
                    ? "attention"
                    : "new"
            }
          >
            {customer.vipTier}
          </Badge>
        ) : (
          <Text variant="bodyMd" tone="subdued" as="span">
            No tier
          </Text>
        )}
      </IndexTable.Cell>

      {/* Orders Count Cell */}
      <IndexTable.Cell>
        <Text variant="bodyMd" as="span">
          {customer.ordersCount}
        </Text>
      </IndexTable.Cell>

      {/* Total Spent Cell */}
      <IndexTable.Cell>
        <Text variant="bodyMd" as="span">
          {customer.totalSpent.currencyCode}
          {Number(customer.totalSpent.amount).toFixed(2)}
        </Text>
      </IndexTable.Cell>
    </IndexTable.Row>
  );
}
