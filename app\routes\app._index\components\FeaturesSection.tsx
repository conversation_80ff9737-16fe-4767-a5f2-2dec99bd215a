import { BlockStack, InlineGrid, Text } from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import FeatureTab from "./FeatureTab";

export default function FeaturesSection() {
  const { t } = useTranslation();

  return (
    <BlockStack gap="200">
      <Text as="h2" variant="headingLg">
        {t("home.keyFeatures")}
      </Text>

      <InlineGrid columns={3} gap={"200"}>
        <FeatureTab
          card={{
            buttonText: t("home.features.memberSync.buttonText"),
            title: t("home.features.memberSync.title"),
            description: t("home.features.memberSync.description"),
            to: "/app/settings",
          }}
          url={"/assets/images/member-sync.png"}
        />

        <FeatureTab
          card={{
            buttonText: t("home.features.orderSync.buttonText"),
            title: t("home.features.orderSync.title"),
            description: t("home.features.orderSync.description"),
            to: "/app/settings",
          }}
          url={"/assets/images/order-sync.png"}
        />

        <FeatureTab
          card={{
            buttonText: t("home.features.membership.buttonText"),
            disabled: true,
            title: t("home.features.membership.title"),
            description: t("home.features.membership.description"),
          }}
          url={"/assets/images/membership-system.png"}
        />
      </InlineGrid>
    </BlockStack>
  );
}
