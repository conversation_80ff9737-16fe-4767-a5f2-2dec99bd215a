import { createReadableStreamFromReadable, type EntryContext } from "@remix-run/node";
import { RemixServer } from "@remix-run/react";
import { createInstance } from "i18next";
import Backend from "i18next-fs-backend/cjs";
import { isbot } from "isbot";
import { resolve } from "node:path";
import { renderToPipeableStream } from "react-dom/server";
import { I18nextProvider, initReactI18next } from "react-i18next";
import { PassThrough } from "stream";
import i18n from "./i18n";
import i18next from "./i18next.server";
import { addDocumentResponseHeaders } from "./shopify.server";

const ABORT_DELAY = 5000;

export default async function handleRequest(
  request: Request,
  responseStatusCode: number,
  responseHeaders: Headers,
  remixContext: EntryContext,
) {
  addDocumentResponseHeaders(request, responseHeaders);
  const callbackName = isbot(request.headers.get("user-agent")) ? "onAllReady" : "onShellReady";

  const instance = createInstance();
  const lng = await i18next.getLocale(request);
  const ns = i18next.getRouteNamespaces(remixContext);

  await instance
    .use(initReactI18next)
    .use(Backend)
    .init({
      ...i18n,
      lng,
      ns,
      backend: { loadPath: resolve("./public/locales/{{lng}}/{{ns}}.json") },
      detection: {
        order: ["querystring", "htmlTag", "cookie", "localStorage", "navigator"],
        caches: [],
      },
    });

  return new Promise((resolve, reject) => {
    let didError = false;

    const { pipe, abort } = renderToPipeableStream(
      <I18nextProvider i18n={instance}>
        <RemixServer context={remixContext} url={request.url} />
      </I18nextProvider>,
      {
        [callbackName]: () => {
          const body = new PassThrough();
          const stream = createReadableStreamFromReadable(body);
          responseHeaders.set("Content-Type", "text/html");

          resolve(
            new Response(stream, {
              headers: responseHeaders,
              status: didError ? 500 : responseStatusCode,
            }),
          );

          pipe(body);
        },
        onShellError(error: unknown) {
          reject(error);
        },
        onError(error: unknown) {
          didError = true;

          console.error(error);
        },
      },
    );

    setTimeout(abort, ABORT_DELAY);
  });
}

// Initialize cron jobs only on server startup with proper error handling
let cronJobsInitialized = false;

if (typeof window === "undefined" && !cronJobsInitialized) {
  initializeCronJobs();
}

async function initializeCronJobs() {
  if (cronJobsInitialized) return;

  try {
    console.log("Starting server initialization...");

    // Dynamic import with error handling to prevent vite bundling issues
    const tasksModule = await import("./utils/tasks.server").catch((err) => {
      console.warn("Could not load tasks.server module:", err.message);
      return null;
    });

    if (!tasksModule || !tasksModule.default) {
      console.warn("Cron jobs module not available - running without background tasks");
      return;
    }

    const runCronJob = tasksModule.default;
    const cronJobs = runCronJob();
    cronJobs.start();

    cronJobsInitialized = true;

    // Graceful shutdown handlers
    const shutdownHandler = (signal: string) => {
      console.log(` ${signal} received, stopping cron jobs...`);
      cronJobs.stop();
      setTimeout(() => process.exit(0), 1000);
    };

    process.on("SIGTERM", () => shutdownHandler("SIGTERM"));
    process.on("SIGINT", () => shutdownHandler("SIGINT"));

    console.log(" Server initialization completed with cron jobs");
  } catch (error) {
    console.error("Failed to initialize server:", error);
    console.log("Server will continue without cron jobs");
  }
}
