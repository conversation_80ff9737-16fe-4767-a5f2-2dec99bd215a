import { Modal, TitleBar } from "@shopify/app-bridge-react";
import { Text } from "@shopify/polaris";
import { useCallback } from "react";
import { useTranslation } from "react-i18next";
import { MODAL_IDS } from "../constants";

interface DeleteVIPTierConfirmationModalProps {
  tierId?: number;
  tierName?: string;
  onConfirm: (tierId?: number) => void;
  onCancel: () => void;
}

export default function DeleteVIPTierConfirmationModal({
  tierId,
  tierName,
  onConfirm,
  onCancel,
}: Readonly<DeleteVIPTierConfirmationModalProps>) {
  const { t } = useTranslation();

  const handleConfirm = useCallback(() => {
    onConfirm(tierId);
  }, [onConfirm, tierId]);

  return (
    <Modal id={MODAL_IDS.DELETE_VIP_TIER_CONFIRMATION}>
      <div className="m-3">
        <Text variant="bodyMd" as="p">
          {t("loyalties.vip.tiers.deleteConfirmation.message", { tierName })}
        </Text>
      </div>
      <TitleBar title={t("loyalties.vip.tiers.deleteConfirmation.title")}>
        <button onClick={onCancel}>{t("loyalties.vip.tiers.deleteConfirmation.cancel")}</button>
        <button onClick={handleConfirm} variant={"primary"}>
          {t("loyalties.vip.tiers.deleteConfirmation.delete")}
        </button>
      </TitleBar>
    </Modal>
  );
}
