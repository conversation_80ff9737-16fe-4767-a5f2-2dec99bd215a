import { useF<PERSON>cher, useLoaderD<PERSON>, useNavigate, useSearchParams } from "@remix-run/react";
import {
  Banner,
  BlockStack,
  Button,
  Card,
  Checkbox,
  InlineGrid,
  InlineStack,
  Layout,
  List,
  Modal,
  Page,
  Select,
  Spinner,
  Text,
  TextField,
} from "@shopify/polaris";
import { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { action } from "./action";
import { VipTierDiscounts } from "./component/VipTierComponent";
import { loader } from "./loader";
import { ActionResponse, LoaderData, TierDiscount } from "./types";

import { useAppBridge } from "@shopify/app-bridge-react";
import EditAmountOffModal from "./edit_modal";

// Export the action and loader functions
export { action, loader };

export default function PointsRewardBirthday() {
  const data = useLoaderData<LoaderData>();
  const [searchParams] = useSearchParams();
  const fetcher = useFetcher<ActionResponse>();

  const [hasDifferentTiers, setHasDifferentTiers] = useState<boolean>(
    data.hasDifferentTiers || false,
  );
  const [hasDiscount, setHasDiscount] = useState<boolean>(!!data.id || data.rewardValue > 0);

  const [title, setTitle] = useState<string>(data.title);
  const [isExpiryEnabled, setIsExpiryEnabled] = useState<boolean>(!!data.expiryMonths);
  const [expiryMonths, setExpiryMonths] = useState<string>(String(data.expiryMonths ?? 0));

  // Single discount states (when hasDifferentTiers = false)
  const [rewardValue, setRewardValue] = useState<number>(data.rewardValue ?? 0);
  const [discountType, setDiscountType] = useState<"percentage" | "fixed">(
    data.discountType === "PERCENTAGE" || data.discountType === "FIXED"
      ? (data.discountType.toLowerCase() as "percentage" | "fixed")
      : "fixed",
  );
  const [minimumRequirement, setMinimumRequirement] = useState<"none" | "amount" | "quantity">(
    data.minimumRequirement === "NONE" ||
      data.minimumRequirement === "AMOUNT" ||
      data.minimumRequirement === "QUANTITY"
      ? (data.minimumRequirement.toLowerCase() as "none" | "amount" | "quantity")
      : "none",
  );
  const [minimumValue, setMinimumValue] = useState<number | null>(data.minimumValue);
  const [combinations, setCombinations] = useState<{
    product: boolean;
    order: boolean;
    shipping: boolean;
  }>({
    product: data.productDiscounts || false,
    order: data.orderDiscounts || false,
    shipping: data.shippingDiscounts || false,
  });

  // Tier-based discount states
  const [tierDiscounts, setTierDiscounts] = useState<TierDiscount[]>(data.tierDiscounts || []);

  // Modal states for tier discount editing
  const [selectedTierDiscount, setSelectedTierDiscount] = useState<TierDiscount | null>(null);
  const [isTierModalOpen, setIsTierModalOpen] = useState<boolean>(false);
  const [tierModalRewardValue, setTierModalRewardValue] = useState<number>(0);
  const [tierModalDiscountType, setTierModalDiscountType] = useState<"percentage" | "fixed">(
    "fixed",
  );
  const [tierModalMinimumRequirement, setTierModalMinimumRequirement] = useState<
    "none" | "amount" | "quantity"
  >("none");
  const [tierModalMinimumValue, setTierModalMinimumValue] = useState<number | null>(null);

  // State for error handling
  const [errorMessage, setErrorMessage] = useState<string>("");
  const [successMessage, setSuccessMessage] = useState<string>("");

  const navigate = useNavigate();
  const settingsId = searchParams.get("settingsId");
  const shopify = useAppBridge();

  const { t } = useTranslation();

  const EDIT_AMOUNT_OFF_MODAL_ID = "edit-amount-off-modal";

  // Update hasDiscount when rewardValue changes
  useEffect(() => {
    setHasDiscount(!!data.id || rewardValue > 0 || tierDiscounts.some((t) => t.hasDiscount));
  }, [data.id, rewardValue, tierDiscounts]);

  // Initialize tier discounts when enabling tier-based discounts
  useEffect(() => {
    if (hasDifferentTiers && tierDiscounts.length === 0 && data.vipTiers.length > 0) {
      const initialTierDiscounts: TierDiscount[] = data.vipTiers.map((tier) => ({
        tierId: tier.id,
        tierName: tier.name,
        rewardValue: 0,
        discountType: "fixed" as const,
        minimumRequirement: "none" as const,
        minimumValue: null,
        hasDiscount: false,
      }));
      setTierDiscounts(initialTierDiscounts);
    } else if (!hasDifferentTiers) {
      setTierDiscounts([]);
    }
  }, [hasDifferentTiers, data.vipTiers]);

  // Check fetcher state for errors
  useEffect(() => {
    if (fetcher.state === "idle" && fetcher.data) {
      if (fetcher.data.error) {
        setErrorMessage(
          typeof fetcher.data.error === "string" ? fetcher.data.error : "An error occurred",
        );
        setSuccessMessage("");
      } else if (fetcher.data.success) {
        setSuccessMessage(
          fetcher.data.message || "Discount has been synced with Shopify successfully!",
        );
        setErrorMessage("");
      } else {
        setSuccessMessage("Discount has been synced with Shopify successfully!");
        setErrorMessage("");
      }
    }
  }, [fetcher.state, fetcher.data]);

  const handleEditModalOpen = useCallback(() => {
    shopify?.modal?.show(EDIT_AMOUNT_OFF_MODAL_ID);
  }, [shopify]);

  const handleHasDifferentTiersChange = useCallback((checked: boolean) => {
    setHasDifferentTiers(checked);
  }, []);

  // Handle tier discount creation
  const handleCreateTierDiscount = useCallback(
    (tierId: number, tierName: string) => {
      const existingDiscount = tierDiscounts.find((t) => t.tierId === tierId);

      if (existingDiscount) {
        setSelectedTierDiscount(existingDiscount);
        setTierModalRewardValue(existingDiscount.rewardValue);
        setTierModalDiscountType(existingDiscount.discountType);
        setTierModalMinimumRequirement(existingDiscount.minimumRequirement);
        setTierModalMinimumValue(existingDiscount.minimumValue);
      } else {
        setSelectedTierDiscount({
          tierId,
          tierName,
          rewardValue: 0,
          discountType: "fixed",
          minimumRequirement: "none",
          minimumValue: null,
          hasDiscount: false,
        });
        setTierModalRewardValue(0);
        setTierModalDiscountType("fixed");
        setTierModalMinimumRequirement("none");
        setTierModalMinimumValue(null);
      }

      setIsTierModalOpen(true);
    },
    [tierDiscounts],
  );

  // Handle tier discount editing
  const handleEditTierDiscount = useCallback((tierDiscount: TierDiscount) => {
    setSelectedTierDiscount(tierDiscount);
    setTierModalRewardValue(tierDiscount.rewardValue);
    setTierModalDiscountType(tierDiscount.discountType);
    setTierModalMinimumRequirement(tierDiscount.minimumRequirement);
    setTierModalMinimumValue(tierDiscount.minimumValue);
    setIsTierModalOpen(true);
  }, []);

  // Save tier discount
  const handleSaveTierDiscount = useCallback(() => {
    if (!selectedTierDiscount) return;

    const updatedTierDiscount: TierDiscount = {
      ...selectedTierDiscount,
      rewardValue: tierModalRewardValue,
      discountType: tierModalDiscountType,
      minimumRequirement: tierModalMinimumRequirement,
      minimumValue: tierModalMinimumValue,
      hasDiscount: tierModalRewardValue > 0,
    };

    setTierDiscounts((prev) => {
      const existingIndex = prev.findIndex((t) => t.tierId === selectedTierDiscount.tierId);
      if (existingIndex >= 0) {
        const updated = [...prev];
        updated[existingIndex] = updatedTierDiscount;
        return updated;
      } else {
        return [...prev, updatedTierDiscount];
      }
    });

    setIsTierModalOpen(false);
    setSelectedTierDiscount(null);
  }, [
    selectedTierDiscount,
    tierModalRewardValue,
    tierModalDiscountType,
    tierModalMinimumRequirement,
    tierModalMinimumValue,
  ]);

  const handleSaveReward = useCallback(async () => {
    setErrorMessage("");
    setSuccessMessage("");

    // Check if discount is configured
    const hasValidDiscount = hasDifferentTiers
      ? tierDiscounts.some((tier) => tier.rewardValue > 0)
      : rewardValue > 0;

    if (!hasValidDiscount) {
      setErrorMessage("Please configure a discount first by clicking Edit or Create.");
      return;
    }

    const payload = {
      id: data.id,
      settingsId: settingsId,
      title: title,
      expiryMonths: isExpiryEnabled ? expiryMonths : undefined,
      combinations: combinations,
      hasDifferentTiers: hasDifferentTiers,
    };

    if (hasDifferentTiers) {
      // Validate tier discounts
      const validTierDiscounts = tierDiscounts.filter((tier) => tier.rewardValue > 0);
      if (validTierDiscounts.length === 0) {
        setErrorMessage("Please set valid discount values for at least one tier.");
        return;
      }

      // Send tier-based discounts
      Object.assign(payload, {
        tierDiscounts: validTierDiscounts,
      });
    } else {
      // Validate single discount
      if (rewardValue <= 0) {
        setErrorMessage("Please set a valid discount value.");
        return;
      }

      // Send single discount
      Object.assign(payload, {
        rewardValue: rewardValue,
        minimumRequirement: minimumRequirement,
        discountType: discountType,
        minimumValue: minimumValue,
      });
    }

    fetcher.submit(JSON.stringify(payload), {
      method: data.id ? "PATCH" : "POST",
      encType: "application/json",
    });
  }, [
    data.id,
    fetcher,
    settingsId,
    title,
    isExpiryEnabled,
    expiryMonths,
    combinations,
    hasDifferentTiers,
    tierDiscounts,
    rewardValue,
    discountType,
    minimumRequirement,
    minimumValue,
  ]);

  const handleDeleteReward = useCallback(() => {
    setErrorMessage("");
    setSuccessMessage("");

    if (!data.id) {
      setErrorMessage("No reward to delete.");
      return;
    }

    fetcher.submit(
      JSON.stringify({
        id: data.id,
      }),
      {
        method: "DELETE",
        encType: "application/json",
      },
    );
  }, [fetcher, data.id]);

  // Navigate after successful save/delete
  useEffect(() => {
    if (
      fetcher.state === "idle" &&
      fetcher.data &&
      !fetcher.data.error &&
      (fetcher.data.success || fetcher.data.id)
    ) {
      const timer = setTimeout(() => {
        navigate(`/app/loyalties/birthday/`);
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [fetcher.state, fetcher.data, navigate]);

  return (
    <Page title="Amount off order">
      <Layout>
        <InlineGrid columns={["twoThirds", "oneThird"]}>
          <Layout.Section>
            {/* Error/Success Messages */}
            {errorMessage && (
              <div style={{ marginBottom: "1rem" }}>
                <Banner tone="critical" title="Error">
                  {errorMessage}
                </Banner>
              </div>
            )}

            {successMessage && (
              <div style={{ marginBottom: "1rem" }}>
                <Banner tone="success" title="Success">
                  {successMessage}
                </Banner>
              </div>
            )}

            {/* Loading State */}
            {fetcher.state !== "idle" && (
              <div style={{ marginBottom: "1rem" }}>
                <Banner tone="info">
                  <InlineStack gap="200" align="start">
                    <Spinner accessibilityLabel="Loading" size="small" />
                    <Text as="span">
                      {fetcher.state === "submitting" ? "Syncing with Shopify..." : "Processing..."}
                    </Text>
                  </InlineStack>
                </Banner>
              </div>
            )}

            <div style={{ marginTop: "1rem" }}>
              <Card>
                <TextField
                  label={
                    <Text as="h2" variant="headingMd">
                      {t("birthday.points.titleHeader")}
                    </Text>
                  }
                  value={title}
                  onChange={setTitle}
                  autoComplete="off"
                  helpText={t("birthday.points.titleHelpText")}
                />
              </Card>
            </div>

            <div style={{ marginTop: "1rem" }}>
              <Card>
                <BlockStack gap="400">
                  <Text as="h2" variant="headingMd">
                    {t("birthday.points.rewardValue")}
                  </Text>

                  <Checkbox
                    label="Different value based on VIP Tiers"
                    checked={hasDifferentTiers}
                    onChange={handleHasDifferentTiersChange}
                  />

                  {!hasDifferentTiers ? (
                    // Single discount card
                    <Card>
                      <InlineStack align="space-between" gap="200">
                        <BlockStack gap="100" align="center">
                          {rewardValue > 0 ? (
                            <>
                              <Text as="p" fontWeight="bold">
                                Amount off order
                              </Text>
                              <Text as="p" tone="subdued">
                                {discountType === "fixed" && "$"}
                                {rewardValue}
                                {discountType === "percentage" && "%"} off entire order
                              </Text>
                            </>
                          ) : (
                            <Text as="p" tone="subdued">
                              {t("birthday.amountOff.noDiscount")}
                            </Text>
                          )}
                        </BlockStack>
                        <Button
                          variant={rewardValue > 0 ? "plain" : "primary"}
                          onClick={handleEditModalOpen}
                        >
                          {rewardValue > 0 ? "Edit" : "Create"}
                        </Button>
                      </InlineStack>
                    </Card>
                  ) : (
                    // VIP Tier-based discounts
                    <VipTierDiscounts
                      vipTiers={data.vipTiers}
                      tierDiscounts={tierDiscounts}
                      onCreateDiscount={handleCreateTierDiscount}
                      onEditDiscount={handleEditTierDiscount}
                    />
                  )}
                </BlockStack>
              </Card>
            </div>
          </Layout.Section>

          <Layout.Section>
            <div style={{ marginTop: "1rem" }}>
              <Card>
                <Text as="h2" variant="headingMd">
                  {t("birthday.points.rewardDescription")}
                </Text>
                <div style={{ marginTop: "0.5rem" }}>
                  <List>
                    <List.Item>{t("birthday.points.item")}</List.Item>
                    <List.Item>{t("birthday.points.apply")}</List.Item>
                    {hasDifferentTiers && (
                      <List.Item>Different discounts for each VIP tier</List.Item>
                    )}
                    {data.shopifyDiscountId && (
                      <List.Item>Automatically applied in Shopify</List.Item>
                    )}
                  </List>
                </div>
              </Card>
            </div>

            <div style={{ marginTop: "1rem" }}>
              <Card>
                <InlineStack align="space-between" blockAlign="center">
                  <Text as="h2" variant="headingMd">
                    {t("birthday.points.expiry")}
                  </Text>
                  <Button
                    pressed={isExpiryEnabled}
                    onClick={() => setIsExpiryEnabled(!isExpiryEnabled)}
                    tone={isExpiryEnabled ? "critical" : "success"}
                  >
                    {isExpiryEnabled ? "Enabled" : "Disabled"}
                  </Button>
                </InlineStack>
                {isExpiryEnabled ? (
                  <TextField
                    label="Expiry months"
                    value={expiryMonths}
                    onChange={setExpiryMonths}
                    type="number"
                    suffix="month(s)"
                    autoComplete="off"
                  />
                ) : (
                  <Text tone="subdued" as="p">
                    {t("birthday.points.expiryDisabled")}
                  </Text>
                )}
              </Card>
            </div>

            {/* Shopify Integration Status */}
            <div style={{ marginTop: "1rem" }}>
              <Card>
                <BlockStack gap="200">
                  <Text as="h2" variant="headingMd">
                    Shopify Integration
                  </Text>
                  {data.shopifyDiscountId ? (
                    <Banner tone="success">
                      This discount is automatically created and managed in your Shopify admin under
                      Discounts section.
                    </Banner>
                  ) : hasDifferentTiers ? (
                    <Banner tone="info">
                      When you save, separate discounts will be created for each VIP tier in your
                      Shopify admin.
                    </Banner>
                  ) : (
                    <Banner tone="info">
                      When you save this discount, it will be automatically created in your Shopify
                      admin.
                    </Banner>
                  )}
                </BlockStack>
              </Card>
            </div>
          </Layout.Section>
        </InlineGrid>

        <Layout.Section>
          <InlineStack gap="600" align="space-between">
            {data.id ? (
              <Button
                size="large"
                variant="primary"
                tone="critical"
                onClick={handleDeleteReward}
                loading={fetcher.state !== "idle"}
              >
                {t("common.delete")}
              </Button>
            ) : (
              <BlockStack />
            )}
            <Button
              size="large"
              variant="primary"
              onClick={handleSaveReward}
              loading={fetcher.state !== "idle"}
            >
              {t("common.save")}
            </Button>
          </InlineStack>
        </Layout.Section>
      </Layout>

      {/* Single Discount Edit Modal */}
      <EditAmountOffModal
        id={EDIT_AMOUNT_OFF_MODAL_ID}
        rewardValue={rewardValue}
        setRewardValue={setRewardValue}
        discountType={discountType}
        setDiscountType={setDiscountType}
        minimumRequirement={minimumRequirement}
        setMinimumRequirement={setMinimumRequirement}
        minimumValue={minimumValue}
        setMinimumValue={setMinimumValue}
        combinations={combinations}
        setCombinations={setCombinations}
        setHasDiscount={setHasDiscount}
      />

      {/* Tier Discount Edit Modal */}
      <Modal
        open={isTierModalOpen}
        onClose={() => setIsTierModalOpen(false)}
        title={`Configure Discount - ${selectedTierDiscount?.tierName}`}
        primaryAction={{
          content: "Save",
          onAction: handleSaveTierDiscount,
          disabled: tierModalRewardValue <= 0,
        }}
        secondaryActions={[
          {
            content: "Cancel",
            onAction: () => setIsTierModalOpen(false),
          },
        ]}
      >
        <Modal.Section>
          <BlockStack gap="400">
            <InlineGrid columns={2} gap="400">
              <TextField
                label="Discount Value"
                type="number"
                value={tierModalRewardValue.toString()}
                onChange={(value) => setTierModalRewardValue(Number(value))}
                prefix={tierModalDiscountType === "fixed" ? "$" : ""}
                suffix={tierModalDiscountType === "percentage" ? "%" : ""}
                autoComplete="off"
              />

              <Select
                label="Discount Type"
                options={[
                  { label: "Fixed Amount", value: "fixed" },
                  { label: "Percentage", value: "percentage" },
                ]}
                value={tierModalDiscountType}
                onChange={(value) => setTierModalDiscountType(value as "percentage" | "fixed")}
              />
            </InlineGrid>

            <Select
              label="Minimum Requirement"
              options={[
                { label: "No minimum", value: "none" },
                { label: "Minimum amount", value: "amount" },
                { label: "Minimum quantity", value: "quantity" },
              ]}
              value={tierModalMinimumRequirement}
              onChange={(value) =>
                setTierModalMinimumRequirement(value as "none" | "amount" | "quantity")
              }
            />

            {tierModalMinimumRequirement !== "none" && (
              <TextField
                label={
                  tierModalMinimumRequirement === "amount"
                    ? "Minimum Amount ($)"
                    : "Minimum Quantity"
                }
                type="number"
                value={tierModalMinimumValue?.toString() || ""}
                onChange={(value) => setTierModalMinimumValue(value ? Number(value) : null)}
                prefix={tierModalMinimumRequirement === "amount" ? "$" : ""}
                autoComplete="off"
              />
            )}
          </BlockStack>
        </Modal.Section>
      </Modal>
    </Page>
  );
}
