import { Modal, TitleBar } from "@shopify/app-bridge-react";
import { BlockStack, Checkbox, RadioButton, Text, TextField } from "@shopify/polaris";
import { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { FreeShippingRewardInterface, REQUIREMENT_TYPES, REWARD_TYPES } from "./interface";

interface AddFreeShippingRewardModalProps {
  isEditMode?: boolean;
  existingReward?: FreeShippingRewardInterface;
  onSave?: (reward: FreeShippingRewardInterface) => void;
  onClose?: () => void;
}

export default function AddFreeShippingRewardModal({
  isEditMode = false,
  existingReward,
  onSave,
  onClose,
}: Readonly<AddFreeShippingRewardModalProps>) {
  const { t } = useTranslation();

  // Initialize with empty values for add mode, or existing values for edit mode
  const [title, setTitle] = useState(isEditMode && existingReward ? existingReward.title : "");
  const [minimumRequirement, setMinimumRequirement] = useState<
    | typeof REQUIREMENT_TYPES.NONE
    | typeof REQUIREMENT_TYPES.AMOUNT
    | typeof REQUIREMENT_TYPES.QUANTITY
  >(isEditMode && existingReward ? existingReward.minimumRequirement : REQUIREMENT_TYPES.NONE);
  const [minimumValue, setMinimumValue] = useState(
    isEditMode && existingReward && existingReward.minimumValue ? existingReward.minimumValue : "",
  );
  const [productDiscounts, setProductDiscounts] = useState(
    isEditMode && existingReward ? existingReward.combinations?.productDiscounts : false,
  );
  const [orderDiscounts, setOrderDiscounts] = useState(
    isEditMode && existingReward ? existingReward.combinations?.orderDiscounts : false,
  );
  const [titleError, setTitleError] = useState("");

  // Update state when reward changes in edit mode
  useEffect(() => {
    if (isEditMode && existingReward) {
      setTitle(existingReward.title);
      setMinimumRequirement(existingReward.minimumRequirement);
      setMinimumValue(existingReward.minimumValue ?? "");
      setProductDiscounts(existingReward.combinations?.productDiscounts);
      setOrderDiscounts(existingReward.combinations?.orderDiscounts);
      // Reset errors when reward changes
      setTitleError("");
    }
  }, [isEditMode, existingReward]);

  const validateForm = useCallback(() => {
    let isValid = true;

    // Reset errors
    setTitleError("");

    // Validate title
    if (!title.trim()) {
      setTitleError(t("loyalties.rewards.validation.titleRequired"));
      isValid = false;
    }

    return isValid;
  }, [title, t]);

  const handleSave = useCallback(() => {
    if (validateForm() && onSave) {
      if (isEditMode && existingReward) {
        // Update existing reward
        const updatedReward: FreeShippingRewardInterface = {
          ...existingReward,
          title,
          minimumRequirement,
          minimumValue: minimumRequirement !== REQUIREMENT_TYPES.NONE ? minimumValue : undefined,
          combinations: {
            productDiscounts,
            orderDiscounts,
          },
        };
        onSave(updatedReward);
      } else {
        // Create new reward
        const newReward: FreeShippingRewardInterface = {
          id: Date.now().toString(),
          type: REWARD_TYPES.FREE_SHIPPING,
          title,
          minimumRequirement,
          minimumValue: minimumRequirement !== REQUIREMENT_TYPES.NONE ? minimumValue : undefined,
          combinations: {
            productDiscounts,
            orderDiscounts,
          },
        };
        onSave(newReward);
      }
    }
  }, [
    validateForm,
    onSave,
    isEditMode,
    existingReward,
    title,
    minimumRequirement,
    minimumValue,
    productDiscounts,
    orderDiscounts,
  ]);
  // Determine modal ID based on mode
  const modalId = isEditMode ? "edit-free-shipping-reward-modal" : "add-free-shipping-reward-modal";

  // Determine modal title based on mode
  const modalTitle = isEditMode
    ? t("loyalties.rewards.editReward")
    : t("loyalties.rewards.addNewReward");

  // Determine heading based on mode
  const heading = isEditMode
    ? t("loyalties.rewards.editFreeShippingReward")
    : t("loyalties.rewards.freeShippingReward");

  return (
    <Modal id={modalId}>
      <div className="m-3">
        <BlockStack gap="400">
          <Text variant="headingMd" as="h2">
            {heading}
          </Text>

          <BlockStack>
            <Text variant="bodyMd" as="p">
              {t("loyalties.rewards.rewardTitle")}
            </Text>

            <TextField
              label={t("loyalties.rewards.rewardTitle")}
              value={title}
              type="text"
              onChange={setTitle}
              autoComplete="off"
              labelHidden
              error={titleError}
            />
            <Text variant="bodyMd" as="p">
              {t("loyalties.rewards.rewardTitleDescription")}
            </Text>
          </BlockStack>

          <BlockStack>
            <Text variant="bodyMd" as="h2">
              {t("loyalties.rewards.minimumRequirements")}
            </Text>

            <RadioButton
              label={t("loyalties.rewards.noMinimumRequirements")}
              id="no-minimum"
              name="requirement-type"
              checked={minimumRequirement === REQUIREMENT_TYPES.NONE}
              onChange={() => setMinimumRequirement(REQUIREMENT_TYPES.NONE)}
            />

            <RadioButton
              label={t("loyalties.rewards.minimumPurchaseAmount")}
              id="minimum-amount"
              name="requirement-type"
              checked={minimumRequirement === REQUIREMENT_TYPES.AMOUNT}
              onChange={() => setMinimumRequirement(REQUIREMENT_TYPES.AMOUNT)}
            />

            <RadioButton
              label={t("loyalties.rewards.minimumQuantityItems")}
              id="minimum-quantity"
              name="requirement-type"
              checked={minimumRequirement === REQUIREMENT_TYPES.QUANTITY}
              onChange={() => setMinimumRequirement(REQUIREMENT_TYPES.QUANTITY)}
            />

            {minimumRequirement !== REQUIREMENT_TYPES.NONE && (
              <TextField
                label={
                  minimumRequirement === REQUIREMENT_TYPES.AMOUNT
                    ? t("loyalties.rewards.minimumAmount")
                    : t("loyalties.rewards.minimumQuantity")
                }
                type="number"
                value={minimumValue}
                onChange={setMinimumValue}
                autoComplete="off"
              />
            )}
          </BlockStack>

          <BlockStack>
            <Text variant="bodyMd" as="h2">
              {t("loyalties.rewards.combinations")}
            </Text>
            <Text variant="bodyMd" as="p">
              {t("loyalties.rewards.freeShippingCombinations")}
            </Text>
            <Checkbox
              label={t("loyalties.rewards.productDiscounts")}
              checked={productDiscounts}
              onChange={setProductDiscounts}
            />
            <Checkbox
              label={t("loyalties.rewards.orderDiscounts")}
              checked={orderDiscounts}
              onChange={setOrderDiscounts}
            />
          </BlockStack>
        </BlockStack>
      </div>

      <TitleBar title={modalTitle}>
        <button onClick={handleSave} variant={"primary"}>
          {t("common.save")}
        </button>
        <button onClick={onClose}>{t("common.cancel")}</button>
      </TitleBar>
    </Modal>
  );
}
