import { DiscountType, RequirementType, RewardType } from "@prisma/client";
import { LoaderFunctionArgs } from "@remix-run/node";
import { authenticate } from "../../shopify.server";
import db from "../../db.server";

// Define the loader data interface
export interface LoaderData {
  vipTier: {
    id: number;
    name: string;
    spendRequirement: number | null;
    rewards: Array<{
      id: number;
      title: string;
      rewardType: RewardType;
      value: string | null;
      discountType: DiscountType | null;
      minimumRequirement: RequirementType | null;
      minimumValue: number | null;
      productDiscounts: boolean | null;
      orderDiscounts: boolean | null;
      shippingDiscounts: boolean | null;
    }>;
  };
}

// Loader function to fetch the VIP tier data
export async function loader({ params, request }: LoaderFunctionArgs) {
  const { session } = await authenticate.admin(request);
  const tierId = params.id;

  if (!tierId || isNaN(Number(tierId))) {
    throw new Response("Invalid tier ID", { status: 400 });
  }

  // Get the shop information
  const shop = await db.shop.findFirst({
    where: {
      myshopifyDomain: session.shop,
    },
  });

  if (!shop) {
    throw new Response(`Shop not found for domain ${session.shop}`, { status: 404 });
  }

  // Find the VIP tier
  const vipTier = await db.loyaltyVIPTier.findUnique({
    where: {
      id: Number(tierId),
    },
    include: {
      rewards: true,
    },
  });

  if (!vipTier) {
    throw new Response(`VIP tier not found with ID ${tierId}`, { status: 404 });
  }

  return Response.json({ vipTier });
}
