import { Mo<PERSON>, <PERSON>B<PERSON> } from "@shopify/app-bridge-react"
import { Box, Button, InlineStack, Text, Icon, Divider, BlockStack } from "@shopify/polaris"
import { rewardOptions } from './reward_options'
import { useTranslation } from 'react-i18next'

interface AddRewardModalProps {
  id: string
  settingsId: number
  isOpen?: boolean
  onHide?: () => void
  navigate?: (link: string) => void
}

export default function AddRewardModal({ id, settingsId, isOpen, onHide, navigate }: Readonly<AddRewardModalProps>) {
  const { t } = useTranslation();
  return (
    <Modal id={id} open={isOpen} onHide={onHide}>
      <BlockStack gap="400" align="center">
        <div
          style={{
            padding: "16px 20px",
            borderBottom: "1px solid #e1e3e5",
          }}
        >
          {rewardOptions.map((reward, index) => (
            <div key={reward.id}>
              <BlockStack align="center">
                {index > 0 && <Box paddingBlockEnd="200" />}
                <InlineStack align="space-between" blockAlign="center">
                  <InlineStack gap="200" align="start">
                    <Icon source={reward.icon} />
                    <Text as="span" variant="bodyLg">
                      {reward.title}
                    </Text>
                  </InlineStack>
                  <Button onClick={() => { navigate?.(`${reward.link}/new?settingsId=${settingsId}`) }}>
                    {t("common.add")}
                  </Button>
                </InlineStack>
                <Box paddingBlockEnd="200" />
                <Divider />
              </BlockStack>
            </div>
          ))}
        </div>
      </BlockStack>

      <TitleBar title={t("birthday.rewards.modalTitle")} />
    </Modal>
  );
}
