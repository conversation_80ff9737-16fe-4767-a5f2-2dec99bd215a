import { gql } from "@apollo/client/core";

export const TAIWAN_COUNTRY_PHONE_CODE = "+886";

export const memberQuery = gql`
  query ($identifier: CustomerIdentifierInput!) {
    customerByIdentifier(identifier: $identifier) {
      id
      email
      phone
      taxExempt
      emailMarketingConsent {
        marketingState
        marketingOptInLevel
        consentUpdatedAt
      }
      firstName
      lastName
      amountSpent {
        amount
        currencyCode
      }
      smsMarketingConsent {
        marketingState
        marketingOptInLevel
      }
      addresses {
        address1
        city
        country
        phone
        zip
      }
      metafields(first: 10) {
        nodes {
          id
          key
          namespace
          value
        }
      }
    }
  }
`;
