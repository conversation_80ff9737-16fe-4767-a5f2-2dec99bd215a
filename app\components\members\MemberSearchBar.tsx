// File: app/components/members/MemberSearchBar.tsx
import { Button, InlineStack, Select, TextField } from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import { SEARCH_TYPE_OPTIONS } from "../../routes/app.members/constants";

interface MemberSearchBarProps {
  queryValue: string;
  selectedSearchType: string;
  placeholder: string;
  isLoading: boolean;
  onQueryChange: (value: string) => void;
  onSearchTypeChange: (value: string) => void;
  onClearButtonClick: () => void;
  onSearch: () => void;
}

export function MemberSearchBar({
  queryValue,
  selectedSearchType,
  placeholder,
  isLoading,
  onQueryChange,
  onSearchTypeChange,
  onClearButtonClick,
  onSearch,
}: Readonly<MemberSearchBarProps>) {
  const { t } = useTranslation();

  return (
    <InlineStack align="start" blockAlign="center" gap="200">
      <div className="flex-1">
        <TextField
          label=""
          value={queryValue}
          onChange={onQueryChange}
          clearButton
          onClearButtonClick={onClearButtonClick}
          placeholder={placeholder}
          autoComplete="off"
        />
      </div>

      <div className="w-40">
        <Select
          label=""
          labelHidden
          options={SEARCH_TYPE_OPTIONS}
          value={selectedSearchType}
          onChange={onSearchTypeChange}
        />
      </div>

      <div>
        <Button onClick={onSearch} disabled={isLoading}>
          {t("members.search.button")}
        </Button>
      </div>
    </InlineStack>
  );
}
