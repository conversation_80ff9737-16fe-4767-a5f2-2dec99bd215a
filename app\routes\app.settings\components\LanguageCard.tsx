import { useAppBridge } from "@shopify/app-bridge-react";
import { Card, Select } from "@shopify/polaris";
import { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { langeuageOptions } from "../../../i18n";
import { FetcherType } from "../types";

export default function LanguageCard({ fetcher }: { readonly fetcher: FetcherType }) {
  const shopify = useAppBridge();

  const { i18n, t } = useTranslation();
  const [selectedLanguage, setSelectedLanguage] = useState(i18n.language || "en");

  const handleLanguageChange = useCallback(
    (value: string) => {
      setSelectedLanguage(value);
      // First change the language in the UI
      i18n.changeLanguage(value).then(() => {
        // Then call action to set the cookie for server-side language detection
        fetcher.submit({ action: "updateLanguage", language: value }, { method: "POST" });
      });
    },
    [i18n, fetcher],
  );

  // Handle toast notification for language updates
  useEffect(() => {
    if (fetcher.formData && fetcher.formData.get("action") === "updateLanguage") {
      shopify.toast.show(t("settings.languageUpdated") || "Language updated successfully");
    }
  }, [fetcher.formData, shopify, t]);

  return (
    <Card>
      <div className="p-2">
        <div className="space-y-4">
          <div className="space-y-2">
            <label
              htmlFor="language-selector"
              className="block text-sm font-medium text-gray-700 dark:text-gray-300"
            >
              {t("settings.language")}
            </label>
            <Select
              id="language-selector"
              label=""
              labelHidden
              options={langeuageOptions}
              onChange={handleLanguageChange}
              value={selectedLanguage}
            />
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {t("settings.languageDescription")}
            </p>
          </div>
        </div>
      </div>
    </Card>
  );
}
