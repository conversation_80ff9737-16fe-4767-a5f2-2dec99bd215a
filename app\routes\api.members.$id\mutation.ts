import { gql } from "@apollo/client/core";

export const updateCustomerMutation = gql`
  mutation updateCustomerMetafields($input: CustomerInput!) {
    customerUpdate(input: $input) {
      customer {
        id
        email
        phone
        taxExempt
        emailMarketingConsent {
          marketingState
          marketingOptInLevel
          consentUpdatedAt
        }
        firstName
        lastName
        amountSpent {
          amount
          currencyCode
        }
        smsMarketingConsent {
          marketingState
          marketingOptInLevel
        }
        addresses {
          address1
          city
          country
          phone
          zip
        }
        metafields(first: 10) {
          nodes {
            id
            key
            namespace
            value
          }
        }
      }
      userErrors {
        message
        field
      }
    }
  }
`;

export const updateMarketingNotifyMutation = gql`
  mutation customerEmailMarketingConsentUpdate(
    $input: CustomerEmailMarketingConsentUpdateInput!
  ) {
    customerEmailMarketingConsentUpdate(input: $input) {
      userErrors {
        field
        message
      }
    }
  }
`;
