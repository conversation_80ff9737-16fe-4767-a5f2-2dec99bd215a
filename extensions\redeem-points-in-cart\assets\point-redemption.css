/* Base container for the point‐redemption component */
point-redemption {
  display: block;
  margin: 1rem 0;
  --pr-primary-color: #007bff;
  --pr-primary-text: #ffffff;
  /* You can define more CSS custom properties here if you want theme-able colors */
}

/* Style for the “Login to Redeem Points” button */
.pr-login-button {
  padding: 10px 16px;
  font-size: 1rem;
  cursor: pointer;
  background-color: var(--pr-primary-color);
  color: var(--pr-primary-text);
  border: none;
  border-radius: 4px;
  text-align: center;
  display: inline-block;
}

/* Balance and rate text */
.pr-balance,
.pr-rate,
.pr-to-use {
  font-size: 0.9rem;
  margin: 0.25rem 0;
}

/* Checkbox container */
.pr-checkbox-container {
  display: flex;
  align-items: center;
  margin: 0.5rem 0;
}

.pr-checkbox-container input[type="checkbox"] {
  margin-right: 0.5rem;
  transform: scale(1.1);
}

/* Error / unavailable state */
.pr-error {
  color: red;
  font-size: 0.9rem;
}
