import { useFetcher, useLoaderD<PERSON> } from "@remix-run/react";
import { useAppBridge } from "@shopify/app-bridge-react";
import { Layout } from "@shopify/polaris";
import { useEffect } from "react";
import { useTranslation } from "react-i18next";
import EntryMethodDisplay from "./components/EntryMethodDisplay";
import ExpirationSettingsDisplay from "./components/ExpirationSettingsDisplay";
import ValidationMethodDisplay from "./components/ValidationMethodDisplay";
import VIPTiersList from "./components/VIPTiersList";
import {
  DEFAULT_ENTRY_METHOD,
  DEFAULT_EXPIRATION_DAYS,
  DEFAULT_EXPIRATION_TYPE,
  DEFAULT_ORDERS_COUNT,
  DEFAULT_VALIDATION_DAYS,
  DEFAULT_VALIDATION_TYPE,
  UPDATE_ENTRY_METHOD,
  UPDATE_EXPIRATION,
  UPDATE_VALIDATION,
} from "./constants";
import { ActionResponse, LoaderData } from "./types";

// Export the loader and action functions
export { action } from "./actions";
export { loader } from "./loader";

// Add i18n namespace
export const handle = {
  i18n: "translation",
};

export default function LoyaltyVipPage() {
  const { loyaltyProgram } = useLoaderData<LoaderData>();
  const shopify = useAppBridge();
  const fetcher = useFetcher<ActionResponse>();
  const { t } = useTranslation();

  // Effect to handle form submission responses
  useEffect(() => {
    if (fetcher.data && fetcher.state === "idle") {
      if (fetcher.data.success) {
        // Show success toast using App Bridge Toast API
        shopify?.toast.show(
          fetcher.data.message ?? t("loyalties.vip.toastMessages.settingsUpdated"),
          {
            isError: false,
            duration: 4000,
          },
        );
      } else if (fetcher.data.error) {
        // Show error toast using App Bridge Toast API
        shopify?.toast.show(fetcher.data.error, {
          isError: true,
          duration: 4000,
        });
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fetcher.data, fetcher.state]);

  return (
    <Layout>
      <Layout.Section>
        <EntryMethodDisplay
          initialMethod={loyaltyProgram?.vipSettings?.entryMethod ?? DEFAULT_ENTRY_METHOD}
          initialOrdersCount={loyaltyProgram?.vipSettings?.ordersCount ?? DEFAULT_ORDERS_COUNT}
          actionType={UPDATE_ENTRY_METHOD}
        />
      </Layout.Section>

      <Layout.Section>
        <ValidationMethodDisplay
          initialValidationType={
            loyaltyProgram?.vipSettings?.validationType ?? DEFAULT_VALIDATION_TYPE
          }
          initialValidationDays={
            loyaltyProgram?.vipSettings?.validationDays ?? DEFAULT_VALIDATION_DAYS
          }
          defaultValidationType={DEFAULT_VALIDATION_TYPE}
          actionType={UPDATE_VALIDATION}
        />
      </Layout.Section>

      <Layout.Section>
        <VIPTiersList />
      </Layout.Section>

      <Layout.Section>
        <ExpirationSettingsDisplay
          initialExpirationType={
            loyaltyProgram?.vipSettings?.expirationType ?? DEFAULT_EXPIRATION_TYPE
          }
          initialExpirationDays={
            loyaltyProgram?.vipSettings?.expirationDays ?? DEFAULT_EXPIRATION_DAYS
          }
          defaultExpirationType={DEFAULT_EXPIRATION_TYPE}
          actionType={UPDATE_EXPIRATION}
        />
      </Layout.Section>

      <Layout.Section></Layout.Section>
    </Layout>
  );
}
