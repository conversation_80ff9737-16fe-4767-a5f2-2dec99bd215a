import { authenticate } from "@/shopify.server";
import { RefundType } from "@prisma/client";
import type { ActionFunctionArgs } from "@remix-run/node";
import { getPointsSettings } from "../app.loyalties.points/services";
import { getMetafields, getReturn, updateCustomerPointMetafield } from "./services";

/**
 * This webhook is used to trigger when the return is successful.
 * To return the redeem points and deduct reward points for the customer.
 */
export const action = async ({ request }: ActionFunctionArgs) => {
  const { payload, topic, shop, admin } = await authenticate.webhook(request);
  console.log(`Received ${topic} webhook for ${shop}`);

  if (!admin) {
    return new Response("Admin not found", { status: 404 });
  }

  const returnData = await getReturn(admin, payload?.admin_graphql_api_id);

  // Find customer points
  const customerMetafields = await getMetafields(
    admin,
    returnData?.order?.customer?.metafields?.edges,
  );
  const customerPoints = Number(customerMetafields?.find((m) => m.key === "points")?.value ?? 0);

  // Find redeem points
  const orderMetafields = await getMetafields(admin, returnData?.order?.metafields?.edges);
  const redeemPoints = Number(orderMetafields?.find((m) => m.key === "redeemPoints")?.value ?? 0);

  // Get refund amount
  const refundAmount = Number(
    returnData?.refunds?.edges[0]?.node?.totalRefundedSet?.shopMoney?.amount ?? 0,
  );
  const totalAmount = Number(returnData?.order?.totalPriceSet?.shopMoney?.amount ?? 0);

  // Get point setting
  const pointSettings = await getPointsSettings(shop);
  const pointsSettings = pointSettings?.pointsSettings;
  const redeemedRefundType =
    pointsSettings && pointsSettings[0]
      ? pointsSettings[0].redeemedRefundType
      : RefundType.PROPORTIONAL;
  const orderRefundType =
    pointsSettings && pointsSettings[0]
      ? pointsSettings[0].orderRefundType
      : RefundType.PROPORTIONAL;

  let redeem = 0;
  if (redeemPoints > 0 && redeemedRefundType !== RefundType.NONE) {
    if (totalAmount > 0 && refundAmount !== totalAmount) {
      redeem = (refundAmount * redeemPoints) / totalAmount;
    } else {
      redeem = redeemPoints;
    }
  }

  let reward = 0;
  switch (orderRefundType) {
    case RefundType.PROPORTIONAL:
      reward = Math.round(refundAmount);
      break;
    case RefundType.FULL:
      if (returnData?.order?.refunds?.length <= 1) {
        reward = Math.round(totalAmount);
      }
      break;
    default:
      reward = 0;
      break;
  }

  if (redeemedRefundType !== RefundType.NONE || orderRefundType !== RefundType.NONE) {
    const newPoint = Math.floor(customerPoints + redeem - reward);
    await updateCustomerPointMetafield(admin, returnData?.order?.customer?.id, String(newPoint));
  }

  return new Response();
};
