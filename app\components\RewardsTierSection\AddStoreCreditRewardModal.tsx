import { Modal, TitleBar } from "@shopify/app-bridge-react";
import { BlockStack, InlineStack, Text, TextField } from "@shopify/polaris";
import { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { REWARD_TYPES, StoreCreditRewardInterface } from "./interface";

interface AddStoreCreditRewardModalProps {
  isEditMode?: boolean;
  existingReward?: StoreCreditRewardInterface;
  onSave?: (reward: StoreCreditRewardInterface) => void;
  onClose?: () => void;
}

export default function AddStoreCreditRewardModal({
  isEditMode = false,
  existingReward,
  onSave,
  onClose,
}: Readonly<AddStoreCreditRewardModalProps>) {
  const { t } = useTranslation();

  // Initialize with empty values for add mode, or existing values for edit mode
  const [title, setTitle] = useState(isEditMode && existingReward ? existingReward.title : "");
  const [value, setValue] = useState(isEditMode && existingReward ? existingReward.value : "");
  const [titleError, setTitleError] = useState("");
  const [valueError, setValueError] = useState("");

  // Update state when reward changes in edit mode
  useEffect(() => {
    if (isEditMode && existingReward) {
      setTitle(existingReward.title);
      setValue(existingReward.value);
      // Reset errors when reward changes
      setTitleError("");
      setValueError("");
    }
  }, [isEditMode, existingReward]);

  const validateForm = useCallback(() => {
    let isValid = true;

    // Reset errors
    setTitleError("");
    setValueError("");

    // Validate title
    if (!title.trim()) {
      setTitleError(t("loyalties.rewards.validation.titleRequired"));
      isValid = false;
    }

    // Validate value
    if (!value.trim()) {
      setValueError(t("loyalties.rewards.validation.valueRequired"));
      isValid = false;
    } else if (isNaN(Number(value)) || Number(value) <= 0) {
      setValueError(t("loyalties.rewards.validation.valueInvalid"));
      isValid = false;
    }

    return isValid;
  }, [title, value, t]);

  const handleSave = useCallback(() => {
    if (validateForm() && onSave) {
      if (isEditMode && existingReward) {
        // Update existing reward
        const updatedReward: StoreCreditRewardInterface = {
          ...existingReward,
          title,
          value,
        };
        onSave(updatedReward);
      } else {
        // Create new reward
        const newReward: StoreCreditRewardInterface = {
          id: Date.now().toString(),
          type: REWARD_TYPES.STORE_CREDIT,
          title,
          value,
        };
        onSave(newReward);
      }
    }
  }, [onSave, title, value, isEditMode, existingReward, validateForm]);

  // Determine modal ID based on mode
  const modalId = isEditMode ? "edit-store-credit-reward-modal" : "add-store-credit-reward-modal";

  // Determine modal title based on mode
  const modalTitle = isEditMode
    ? t("loyalties.rewards.editReward")
    : t("loyalties.rewards.addNewReward");

  // Determine heading based on mode
  const heading = isEditMode
    ? t("loyalties.rewards.editStoreCreditReward")
    : t("loyalties.rewards.storeCreditReward");

  return (
    <Modal id={modalId}>
      <div className="m-3">
        <BlockStack gap="400">
          <Text variant="headingMd" as="h2">
            {heading}
          </Text>

          <BlockStack>
            <Text variant="bodyMd" as="p">
              {t("loyalties.rewards.rewardTitle")}
            </Text>
            <TextField
              label={t("loyalties.rewards.rewardTitle")}
              value={title}
              type="text"
              onChange={setTitle}
              autoComplete="off"
              labelHidden
              error={titleError}
            />
            <Text variant="bodyMd" as="p">
              {t("loyalties.rewards.rewardTitleDescription")}
            </Text>
          </BlockStack>

          <BlockStack>
            <Text variant="bodyMd" as="p">
              {t("loyalties.rewards.rewardValue")}
            </Text>
            <InlineStack gap="200" align="start" blockAlign="center">
              <TextField
                label={t("loyalties.rewards.rewardValue")}
                type="number"
                value={value}
                onChange={setValue}
                autoComplete="off"
                labelHidden
                error={valueError}
              />

              <Text variant="bodyMd" as="p">
                {t("loyalties.rewards.storeCredits")}
              </Text>
            </InlineStack>
            <Text variant="bodyMd" as="p">
              {t("loyalties.rewards.rewardValueDescription")}
            </Text>
          </BlockStack>
        </BlockStack>
      </div>
      <TitleBar title={modalTitle}>
        <button onClick={handleSave} variant={"primary"}>
          {t("common.save")}
        </button>
        <button onClick={onClose}>{t("common.cancel")}</button>
      </TitleBar>
    </Modal>
  );
}
