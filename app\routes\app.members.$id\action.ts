import { LoaderFunctionArgs, json } from "@remix-run/node";
import { authenticate } from "../../shopify.server";

export async function action({ request, params }: LoaderFunctionArgs) {
  const { admin } = await authenticate.admin(request);
  const { id } = params;

  if (!id) return json({ success: false, error: "Customer ID is required" });

  const formData = await request.formData();
  const birthday = formData.get("birthday") as string;

  if (!birthday) return json({ success: false, error: "Birthday is required" });

  try {
    const shopifyCustomerId = id.includes("gid:") ? id : `gid://shopify/Customer/${id}`;
    const response = await admin.graphql(
      `mutation customerUpdate($input: CustomerInput!) {
        customerUpdate(input: $input) {
          customer {
            id
          }
          userErrors {
            field
            message
          }
        }
      }`,
      {
        variables: {
          input: {
            id: shopifyCustomerId,
            metafields: [
              {
                namespace: "custom",
                key: "birthday",
                value: birthday,
                type: "single_line_text_field",
              },
            ],
          },
        },
      },
    );

    const responseJson = await response.json();

    if (responseJson.data?.customerUpdate?.userErrors?.length > 0) {
      return json({
        success: false,
        error: responseJson.data.customerUpdate.userErrors[0].message,
      });
    }

    return json({ success: true });
  } catch (error) {
    console.error("Error updating customer:", error);
    return json({ success: false, error: String(error) });
  }
}
