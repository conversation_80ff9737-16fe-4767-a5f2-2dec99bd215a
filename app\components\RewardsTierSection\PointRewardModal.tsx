import { useNavigate } from "@remix-run/react";
import { Mo<PERSON>, TitleBar } from "@shopify/app-bridge-react";
import {
  BlockStack,
  Box,
  Button,
  Checkbox,
  Form,
  InlineStack,
  Text,
  TextField,
} from "@shopify/polaris";
import { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import {
  LoyaltyPointsData,
  LoyaltyVIPTierInterface,
  MODAL_IDS,
  PointRewardInterface,
  REWARD_TYPES,
  TiersRuleInterface,
} from "./interface";

interface PointRewardModalProps {
  /**
   * Modal mode - "add" for creating new rewards, "edit" for updating existing ones
   */
  mode: "add" | "edit";

  /**
   * The reward to edit (required in edit mode)
   */
  reward?: PointRewardInterface;

  /**
   * VIP Tiers
   */
  vipTiers?: LoyaltyVIPTierInterface[];

  /**
   * Points Settings
   */
  pointSettings?: LoyaltyPointsData;

  /**
   * Callback function when the reward is saved
   */
  onSave?: (reward: PointRewardInterface, existingvipTiers: LoyaltyVIPTierInterface[]) => void;

  /**
   * Callback function when the modal is closed
   */
  onClose?: () => void;
}

export default function PointRewardModal({
  mode,
  reward,
  onSave,
  onClose,
  vipTiers = [],
  pointSettings,
}: Readonly<PointRewardModalProps>) {
  const { t } = useTranslation();
  const navigate = useNavigate();

  // Initialize with empty values for add mode, or existing values for edit mode
  // Basic reward‐fields
  const [title, setTitle] = useState(mode === "edit" && reward ? reward.title : "");
  const [value, setValue] = useState(mode === "edit" && reward ? reward.value : "0");

  // Our in‐modal copy of the tiers
  const [tiers, setTiers] = useState<TiersRuleInterface[]>([]);

  const [titleError, setTitleError] = useState("");
  const [valueError, setValueError] = useState("");
  const [useDifferent, setUseDifferent] = useState(vipTiers[0].basedOnDiffTier ?? false);
  const [editingTiers, setEditingTiers] = useState(false);

  // Update state when reward changes in edit mode
  useEffect(() => {
    if (mode === "edit" && reward) {
      setTitle(reward.title);
      setValue(reward.value);
      // Reset errors when reward changes
      setTitleError("");
      setValueError("");
    }
    // Build our local tier state
    setTiers(
      vipTiers.map((t) => ({
        ...t,
        spendAmountStr: t.spendAmount?.toString() ?? "1",
        pointEarnStr: t.pointEarn?.toString() ?? "1",
      })),
    );
  }, [mode, reward, vipTiers]);

  const validateForm = useCallback(() => {
    let isValid = true;

    // Reset errors
    setTitleError("");
    setValueError("");

    // Validate title
    if (!title.trim()) {
      setTitleError(t("loyalties.rewards.validation.titleRequired"));
      isValid = false;
    }

    // Validate value
    if (!value.trim()) {
      setValueError(t("loyalties.rewards.validation.valueRequired"));
      isValid = false;
    } else if (isNaN(Number(value)) || Number(value) <= 0) {
      setValueError(t("loyalties.rewards.validation.valueInvalid"));
      isValid = false;
    }

    return isValid;
  }, [title, value, t]);

  const handleSave = useCallback(() => {
    setEditingTiers(false);
    // Create new reward
    const newReward: PointRewardInterface = {
      id: Date.now().toString(),
      type: REWARD_TYPES.POINTS,
      title,
      value,
    };
    const payloadTiers: LoyaltyVIPTierInterface[] = tiers.map((origTier) => ({
      ...origTier,
      spendAmount: Number(origTier.spendAmountStr),
      pointEarn: Number(origTier.pointEarnStr),
      basedOnDiffTier: useDifferent,
    }));
    if (onSave) {
      onSave(newReward, payloadTiers);
    }
  }, [mode, onSave, reward, title, value, validateForm, tiers, useDifferent]);

  // Determine modal ID based on mode
  const modalId = mode === "edit" ? MODAL_IDS.EDIT_POINT_REWARD : MODAL_IDS.ADD_POINT_REWARD;

  // Determine modal title based on mode
  const modalTitle =
    mode === "edit" ? t("loyalties.rewards.editReward") : t("loyalties.rewards.addNewReward");

  // Determine heading based on mode
  const heading =
    mode === "edit" ? t("loyalties.rewards.editPointReward") : t("loyalties.rewards.pointReward");

  const handleSubmit = useCallback(() => {
    handleSave();
  }, [handleSave]);

  const handleClose = useCallback(() => {
    setEditingTiers(false);
    if (onClose) {
      onClose();
    }
  }, [onClose]);

  const handleUseDifferent = useCallback(() => {
    setUseDifferent((prev) => {
      const next = !prev;
      return next;
    });
  }, []);

  const handleClickEdit = useCallback(() => {
    navigate("/app/loyalties/points");
  }, [navigate]);

  return (
    <Modal id={modalId}>
      <div className="m-3">
        <Form onSubmit={handleSubmit}>
          <BlockStack gap="200">
            <Text variant="headingMd" as="h2">
              {heading}
            </Text>
            <BlockStack gap={"300"}>
              <Text variant="bodyMd" as="h3">
                {t("loyalties.rewards.rewardValue")}
              </Text>
              <Checkbox
                label={t("loyalties.rewards.diffpointDescription")}
                checked={useDifferent}
                onChange={handleUseDifferent}
              />
              <BlockStack gap={"300"}>
                <Box
                  as="div"
                  borderColor="border-disabled"
                  borderStyle="solid"
                  borderWidth="025"
                  borderRadius="150"
                  padding={"300"}
                >
                  <InlineStack as="div" gap="200" align="space-between" blockAlign="center">
                    {useDifferent && (
                      <>
                        <Text variant="bodyMd" as="p">
                          {t("placeOrder.tierStarter")}
                        </Text>
                      </>
                    )}
                  </InlineStack>
                  <InlineStack as="div" gap="200" align="space-between" blockAlign="center">
                    <Text variant="bodyMd" as="p">
                      {pointSettings?.pointsPerCurrency} {t("loyalties.rewards.pointForSpent")}{" "}
                      {pointSettings?.currencyAmount} {t("loyalties.rewards.spent")}
                    </Text>
                    <Button variant="plain" tone="success" onClick={handleClickEdit}>
                      {t("placeOrder.editInPoints")}
                    </Button>
                  </InlineStack>
                </Box>

                {useDifferent &&
                  tiers.length > 0 &&
                  tiers.map((tier, index) => (
                    <Box
                      key={index}
                      as="div"
                      borderColor="border-disabled"
                      borderStyle="solid"
                      borderWidth="025"
                      borderRadius="150"
                      padding={"300"}
                    >
                      <InlineStack as="div" gap="200" align="space-between" blockAlign="center">
                        <>
                          <Text variant="bodyMd" as="p">
                            {/* If you want to display a name, ensure it's present in the tier object, otherwise show index or leave blank */}
                            {tiers[index]?.name ?? ""}
                          </Text>
                        </>
                      </InlineStack>
                      <InlineStack as="div" gap="200" align="start" blockAlign="center">
                        {!editingTiers && (
                          <>
                            <Text variant="bodyMd" as="p">
                              {tier.spendAmount ? tier.spendAmount : 1}
                              {" " + t("loyalties.rewards.pointForSpent")}
                              {tier.pointEarn ? tier.pointEarn : 1}{" "}
                              {" " + t("loyalties.rewards.spent")}
                            </Text>
                          </>
                        )}
                        {editingTiers && (
                          <>
                            <InlineStack as="div" align="start" blockAlign="center" gap={"200"}>
                              <TextField
                                label=""
                                type="text"
                                size="slim"
                                value={tier.spendAmountStr}
                                onChange={(v) => {
                                  setTiers((prevTiers) => {
                                    const updated = [...prevTiers];
                                    updated[index].spendAmountStr = v;
                                    return updated;
                                  });
                                }}
                                autoComplete="off"
                                min={1}
                                maxLength={10}
                                labelHidden
                                autoSize
                              />
                              <Text variant="bodyMd" as="p">
                                {t("loyalties.rewards.pointForSpent")}
                              </Text>
                              <TextField
                                label=""
                                type="text"
                                size="slim"
                                value={tier.pointEarnStr}
                                onChange={(v) => {
                                  setTiers((prevTiers) => {
                                    const updated = [...prevTiers];
                                    updated[index].pointEarnStr = v;
                                    return updated;
                                  });
                                }}
                                autoComplete="off"
                                min={1}
                                maxLength={10}
                                labelHidden
                                autoSize
                              />
                              <Text variant="bodyMd" as="p">
                                {t("loyalties.rewards.spent")}
                              </Text>
                            </InlineStack>
                          </>
                        )}
                      </InlineStack>
                    </Box>
                  ))}
              </BlockStack>
            </BlockStack>
          </BlockStack>
        </Form>
      </div>
      <TitleBar title={modalTitle}>
        {!editingTiers && (
          <>
            <button onClick={() => setEditingTiers(!editingTiers)}>{t("common.editValue")}</button>
          </>
        )}

        {editingTiers && (
          <>
            <button onClick={handleSave} variant={"primary"}>
              {t("common.save")}
            </button>
            <button onClick={handleClose}>{t("common.cancel")}</button>
          </>
        )}
      </TitleBar>
    </Modal>
  );
}
