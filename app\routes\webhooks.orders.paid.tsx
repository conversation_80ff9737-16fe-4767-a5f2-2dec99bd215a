import type { ActionFunctionArgs } from "@remix-run/node";
import { GET_CUSTOMER_POINTS, UPSERT_CUSTOMER_POINTS } from "../graphql/memberQueries";
import { authenticate } from "../shopify.server";
import { MemberMetafield } from "../types/memberTypes";
import {
  getPointOrderSetting,
  getPointsAccoringVipSettings,
  getPointsSettings,
  saveDelayedPoints,
} from "./app.loyalties.points/services";
import { DEFAULT_ENTRY_METHOD } from "./app.loyalties.vip/constants";
import { getVipSettings } from "./app.loyalties.vip/services";
import {
  getNamespaceMetafield,
  updateCustomerPointMetafield,
  updateCustomerTierMetafield,
  updateOrderPointMetafield,
} from "./webhooks.returns.close/services";

export const action = async ({ request }: ActionFunctionArgs) => {
  const { payload, topic, shop, admin } = await authenticate.webhook(request);

  console.log("🚀 Webhook received:", {
    topic,
    shop,
    orderId: payload?.id,
    customerId: payload?.customer?.admin_graphql_api_id,
    orderTotal: payload?.total_price,
    timestamp: new Date().toISOString(),
  });

  // Get the settings for the shop
  if (!admin) {
    console.log("❌ Admin not found for shop:", shop);
    return new Response("Admin not found", { status: 404 });
  }

  // Fetch VIP tiers if the program exists
  console.log("📋 Fetching shop settings...");
  const settings = await getVipSettings(shop);
  const vipTiers = settings?.vipTiers;

  const pointSettings = await getPointsSettings(shop);
  const pointsSettings = pointSettings?.pointsSettings;

  const vipTierSetting = await getPointsAccoringVipSettings(shop);
  const vipTiersSettings = vipTierSetting?.vipTiersSettings;

  const pointOrderSettings = await getPointOrderSetting(shop);
  const issueType = pointOrderSettings?.loyaltyPoints?.pointsIssueType || "IMMEDIATE";
  const issueDays = pointOrderSettings?.loyaltyPoints?.issueDays || 0;

  console.log("⚙️ Shop configuration loaded:", {
    vipTiersCount: vipTiers?.length || 0,
    pointsSettingsExists: !!pointsSettings,
    vipTiersSettingsExists: !!vipTiersSettings,
    issueType,
    issueDays,
    entryMethod: settings?.entryMethod,
  });

  if (admin) {
    console.log("👤 Fetching customer data for:", payload.customer.admin_graphql_api_id);
    const response = await admin.graphql(
      `query getCustomer($id: ID!) {
            customer(id: $id) {
                id
                metafields(first: 10) {
                    edges {
                        node {
                        namespace
                        key
                        value
                        }
                    }
                }
                amountSpent {amount currencyCode}
            }
        }`,
      { variables: { id: payload.customer.admin_graphql_api_id } },
    );
    const responseJson = await response.json();
    const customer = responseJson?.data?.customer;

    console.log("📊 Customer data retrieved:", {
      customerId: customer?.id,
      amountSpent: customer?.amountSpent?.amount,
      currency: customer?.amountSpent?.currencyCode,
      metafieldsCount: customer?.metafields?.edges?.length || 0,
    });

    // Extract metafields
    const metafields = customer.metafields?.edges.map((m: any) => ({
      namespace: m.node.namespace,
      key: m.node.key,
      value: m.node.value,
    })) as MemberMetafield[];
    const namespace = (await getNamespaceMetafield(admin)) as string;

    // Find VIP tier metafield
    const vipTierMetafield = metafields.find(
      (m) => m.namespace === namespace && m.key === "vip_tier",
    );
    const vipTier = vipTierMetafield ? vipTierMetafield.value : null;

    // Check Order Status for upadte Points
    const orderStatus =
      pointsSettings && pointsSettings[0] ? pointsSettings[0].orderStatus : "PAID_FULFILLED";

    console.log("💰 Starting points calculation:", {
      orderStatus,
      orderTotal: payload?.total_price,
      customerTier: vipTier,
    });

    // ────────────────────────────────────────────────────────
    // Compute how many points this order just earned
    // ────────────────────────────────────────────────────────
    let currentAmount = 1;
    let pointsPerCurrency = 1;
    if (vipTiersSettings && vipTiersSettings[0].basedOnDiffTier) {
      // If the points are based on the VIP tier, we need to find the tier of the customer
      const currentTier = vipTiersSettings.find((tier) => tier.name === vipTier);
      currentAmount = currentTier ? (currentTier.spendAmount ?? 1) : 1;
      pointsPerCurrency = currentTier ? (currentTier.pointEarn ?? 1) : 1;
      console.log("🎯 Using VIP tier-based points:", {
        tierName: vipTier,
        spendAmount: currentAmount,
        pointEarn: pointsPerCurrency,
      });
    } else {
      currentAmount = pointsSettings && pointsSettings[0] ? pointsSettings[0].currencyAmount : 1;
      pointsPerCurrency =
        pointsSettings && pointsSettings[0] ? pointsSettings[0].pointsPerCurrency : 1;
      console.log("📈 Using standard points calculation:", {
        currencyAmount: currentAmount,
        pointsPerCurrency,
      });
    }
    const orderAmount = (Number(payload?.total_price) / currentAmount) * pointsPerCurrency;
    const earnedPoints = Math.floor(orderAmount);

    console.log("🧮 Points calculation result:", {
      orderAmount,
      earnedPoints,
      calculation: `(${payload?.total_price} / ${currentAmount}) * ${pointsPerCurrency} = ${orderAmount}`,
    });

    // ────────────────────────────────────────────────────────
    // Read the customer’s existing points via GraphQL
    // ────────────────────────────────────────────────────────
    const customerGid = payload.customer.admin_graphql_api_id;

    console.log("🎁 Processing points issuance:", {
      orderStatus,
      issueType,
      earnedPoints,
      shouldIssuePoints: orderStatus !== "PAID_FULFILLED",
    });

    if (orderStatus !== "PAID_FULFILLED") {
      if (issueType === "IMMEDIATE") {
        console.log("⚡ Issuing points immediately...");
        const getRes = await admin.graphql(GET_CUSTOMER_POINTS, {
          variables: { customerId: customerGid },
        });
        const getJson = await getRes.json();
        const existingMf = getJson.data.customer.pointsMf;
        const currentPoints = existingMf?.value ? Number(existingMf.value) : 0;
        // Calculator Point immediate
        const newTotal = (currentPoints + earnedPoints).toString();

        console.log("💳 Updating customer points:", {
          currentPoints,
          earnedPoints,
          newTotal,
        });

        await admin.graphql(UPSERT_CUSTOMER_POINTS, {
          variables: {
            ownerId: customerGid,
            namespace: "$app",
            key: "points",
            value: newTotal,
          },
        });

        console.log("✅ Points updated successfully");
      } else if (issueType === "DELAYED") {
        // Save for Cron Task
        try {
          const issueAt = new Date(Date.now() + issueDays * 24 * 60 * 60 * 1000);
          console.log("⏰ Scheduling delayed points:", {
            points: earnedPoints,
            issueDays,
            issueAt: issueAt.toISOString(),
          });

          await saveDelayedPoints({
            customerGid,
            orderId: payload.id.toString(),
            points: earnedPoints,
            issueAt,
          });

          console.log("✅ Delayed points scheduled successfully");
        } catch (error) {
          console.error("❌ Error saving delayed points:", error);
        }
      }
    } else {
      console.log("⏭️ Skipping points issuance - order status requires fulfillment");
    }

    // Find points metafield
    const pointsMetafield = metafields.find((m) => m.namespace === namespace && m.key === "points");
    const customerPoints = pointsMetafield ? pointsMetafield.value : 0;

    //Update customer & order points & delete redeem discount
    console.log("🎫 Checking for point redemption discounts...");
    const redeemDiscount = payload?.discount_applications?.find(function (discount: any) {
      return discount.title.includes("OMO_POINTS_");
    });

    if (redeemDiscount && payload?.admin_graphql_api_id) {
      const match = redeemDiscount?.title?.match(/OMO_POINTS_(\d+)/);
      const redeemPoint = match ? parseInt(match[1], 10) : 0;
      const newPoint = Math.floor(Number(customerPoints) - redeemPoint);

      console.log("💸 Processing point redemption:", {
        discountTitle: redeemDiscount.title,
        redeemedPoints: redeemPoint,
        currentPoints: customerPoints,
        newPointBalance: newPoint,
      });

      if (newPoint > 0 && redeemPoint > 0) {
        await updateCustomerPointMetafield(admin, customer.id, String(newPoint));
        await updateOrderPointMetafield(admin, payload.admin_graphql_api_id, String(redeemPoint));
        console.log("✅ Point redemption processed successfully");
      } else {
        console.log("⚠️ Skipping point redemption - invalid point values");
      }
    } else {
      console.log("ℹ️ No point redemption discount found");
    }

    console.log("🏆 Evaluating VIP tier eligibility...");
    let newTier = null;
    if (vipTiers && vipTiers.length > 0) {
      console.log("📊 VIP tier evaluation criteria:", {
        currentTier: vipTier,
        customerSpent: customer?.amountSpent?.amount,
        customerPoints: Number(customerPoints),
        entryMethod: settings?.entryMethod,
        availableTiers: vipTiers.length,
      });

      for (let i = 0; i < vipTiers.length; i++) {
        const spendRequirement = vipTiers[i]?.spendRequirement ?? null;
        const pointsRequirement = vipTiers[i]?.pointsRequirement ?? null;
        const tierName = vipTiers[i].name;

        console.log(`🎯 Checking tier "${tierName}":`, {
          spendRequirement,
          pointsRequirement,
          meetsSpendReq: spendRequirement
            ? customer?.amountSpent?.amount > spendRequirement
            : false,
          meetsPointsReq: pointsRequirement ? Number(customerPoints) > pointsRequirement : false,
        });

        if (
          (spendRequirement &&
            customer?.amountSpent?.amount > spendRequirement &&
            settings?.entryMethod !== DEFAULT_ENTRY_METHOD) ||
          (pointsRequirement &&
            Number(customerPoints) > pointsRequirement &&
            settings?.entryMethod === DEFAULT_ENTRY_METHOD)
        ) {
          newTier = vipTiers[i].name;
          console.log(`✅ Customer qualifies for tier: ${newTier}`);
        }
      }
    } else {
      console.log("ℹ️ No VIP tiers configured");
    }

    //update Customer Tier
    if (newTier && vipTier !== newTier) {
      console.log("🔄 Updating customer VIP tier:", {
        from: vipTier,
        to: newTier,
      });
      await updateCustomerTierMetafield(admin, customer.id, newTier);
      console.log("✅ VIP tier updated successfully");
    } else if (newTier === vipTier) {
      console.log("ℹ️ Customer already has the correct VIP tier");
    } else {
      console.log("ℹ️ No VIP tier update needed");
    }

    // Log webhook processing completion with key results
    console.log("Webhook processing completed with result:", {
      shop,
      topic,
      customerId: payload?.customer?.admin_graphql_api_id,
      orderId: payload?.id,
      earnedPoints,
      issueType,
      currentTier: vipTier,
      newTier,
      orderAmount: payload?.total_price,
      status: "success",
    });
  }

  return new Response();
};
