export enum CustomerStatus {
  ACTIVE = "ACTIVE",
  DELETED = "DELETED",
}

export enum MemberVipTier {
  STARTER = "Starter",
  VIP = "VIP",
  SVIP = "SVIP",
  SSVIP = "SSVIP",
}

export interface Member {
  id: string;
  fullName: string;
  email: string;
  phone: string;
  points: number;
  vipTier: MemberVipTier;
  ordersCount: number;
  totalSpent: {
    amount: number;
    currencyCode: string;
  };
  address?: string;
  birthday?: string;
  socialLinks?: {
    facebook?: string;
    google?: string;
    line?: string;
  };
  avatar?: string;
  createdAt: string;
}

export interface MemberEvent {
  id: string;
  type: "order_placed" | "order_payment" | "vip_upgrade" | "points_earned" | "points_redeemed";
  description: string;
  timestamp: string;
  data?: {
    orderId?: string;
    pointsAmount?: number;
    vipTier?: MemberVipTier;
  };
}

export interface MemberDetails extends Member {
  events: MemberEvent[];
  lifetimeOrders: number;
  lifetimeSpent: {
    amount: number;
    currencyCode: string;
  };
}
