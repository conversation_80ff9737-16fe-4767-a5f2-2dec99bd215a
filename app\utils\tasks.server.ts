import { ApolloClient, gql } from "@apollo/client/core";
import db from "../db.server";
import { clientByShop } from "./auth.server";

// Native cron implementation without node-cron
class NativeCron {
  private intervals: NodeJS.Timeout[] = [];
  private isRunning = false;

  start() {
    if (this.isRunning) return;

    console.log("Starting native cron jobs...");
    this.isRunning = true;

    // Birthday points - daily at midnight
    this.scheduleDaily(0, 0, () => {
      console.log(" Running birthday points job...");
      updatePointsOnBirthday()
        .then(() => console.log(" Birthday points job completed"))
        .catch((err) => console.error(" Birthday points job error:", err));
    });

    // Delayed points - daily at 2:00 AM
    this.scheduleDaily(2, 0, () => {
      console.log(" Running delayed points job...");
      processDelayedPoints()
        .then(() => console.log(" Delayed points job completed"))
        .catch((err) => console.error(" Delayed points job error:", err));
    });

    // Cleanup - weekly on Sunday at 3:00 AM
    this.scheduleWeekly(0, 3, 0, () => {
      console.log(" Running cleanup job...");
      cleanupOldRecords()
        .then(() => console.log(" Cleanup job completed"))
        .catch((err) => console.error(" Cleanup job error:", err));
    });

    console.log(" All native cron jobs started");
  }

  stop() {
    console.log(" Stopping native cron jobs...");
    this.intervals.forEach(clearTimeout);
    this.intervals = [];
    this.isRunning = false;
    console.log(" All native cron jobs stopped");
  }

  private scheduleDaily(hour: number, minute: number, callback: () => void) {
    const runCallback = () => {
      const now = new Date();
      const target = new Date();
      target.setHours(hour, minute, 0, 0);

      // If target time has passed today, schedule for tomorrow
      if (now > target) {
        target.setDate(target.getDate() + 1);
      }

      const delay = target.getTime() - now.getTime();

      const timeout = setTimeout(() => {
        callback();
        // Schedule next run (24 hours later)
        this.scheduleDaily(hour, minute, callback);
      }, delay);

      this.intervals.push(timeout);

      console.log(
        `Scheduled daily job for ${hour}:${minute.toString().padStart(2, "0")} (in ${Math.round(delay / 1000 / 60)} minutes)`,
      );
    };

    runCallback();
  }

  private scheduleWeekly(dayOfWeek: number, hour: number, minute: number, callback: () => void) {
    const runCallback = () => {
      const now = new Date();
      const target = new Date();
      target.setHours(hour, minute, 0, 0);

      // Calculate days until target day of week
      const daysUntilTarget = (dayOfWeek - now.getDay() + 7) % 7;
      if (daysUntilTarget === 0 && now > target) {
        // If it's the target day but time has passed, schedule for next week
        target.setDate(target.getDate() + 7);
      } else {
        target.setDate(target.getDate() + daysUntilTarget);
      }

      const delay = target.getTime() - now.getTime();

      const timeout = setTimeout(() => {
        callback();
        // Schedule next run (7 days later)
        this.scheduleWeekly(dayOfWeek, hour, minute, callback);
      }, delay);

      this.intervals.push(timeout);

      const dayNames = [
        "Sunday",
        "Monday",
        "Tuesday",
        "Wednesday",
        "Thursday",
        "Friday",
        "Saturday",
      ];
      console.log(
        `Scheduled weekly job for ${dayNames[dayOfWeek]} ${hour}:${minute.toString().padStart(2, "0")} (in ${Math.round(delay / 1000 / 60 / 60)} hours)`,
      );
    };

    runCallback();
  }
}

// Semaphore implementation for job locking
class SimpleSemaphore {
  private count: number;
  private queue: (() => void)[] = [];

  constructor(count: number) {
    this.count = count;
  }

  take(callback: () => void) {
    if (this.count > 0) {
      this.count--;
      callback();
    } else {
      this.queue.push(callback);
    }
  }

  leave() {
    this.count++;
    if (this.queue.length > 0) {
      const next = this.queue.shift();
      if (next) {
        this.count--;
        next();
      }
    }
  }
}

// Global semaphore and cron instances
const sem = new SimpleSemaphore(1);
const nativeCron = new NativeCron();

export default function runCronJob() {
  return {
    start: () => {
      nativeCron.start();
    },
    stop: () => {
      nativeCron.stop();
    },
  };
}

// ========================================
// BIRTHDAY POINTS JOB (Existing)
// ========================================

const today = new Date();

interface CustomerQueryResponse {
  data: {
    customers: {
      edges: Array<{
        cursor: string;
        node: {
          id: string;
          metafields: {
            nodes: {
              id: string;
              namespace: string;
              key: string;
              value: string;
            }[];
          };
        };
      }>;
      pageInfo: {
        hasNextPage: boolean;
        endCursor?: string;
      };
    };
  };
}

export async function updatePointsOnBirthday() {
  // Use semaphore to prevent concurrent execution
  return new Promise<void>((resolve, reject) => {
    sem.take(async () => {
      try {
        console.log("Processing birthday points...");

        // Get all shops
        const shops = await db.shop.findMany({
          select: {
            shopId: true,
            myshopifyDomain: true,
            shopToken: true,
          },
        });

        // Loop through each shop and process
        for (const shop of shops) {
          try {
            const client = await clientByShop(shop);
            const namespace = (await getNamespaceMetafield(client)) as string;
            const birthdayRewards = await getBirthdayRewards(shop.shopId);

            let previousEndCursor = null,
              hasNextPage = true;
            while (hasNextPage) {
              // Query a customer chunk
              const customerChunk = await getCustomerChunk(client, previousEndCursor ?? null);

              const birthdayCustomers = filterBirthdayCustomers(customerChunk);

              // Update points for each birthday customer
              for (const customer of birthdayCustomers) {
                const customerId = customer.node.id;
                const customerPoint = Number(
                  customer.node.metafields.nodes.find(
                    (m) => m.namespace === namespace && m.key === "points",
                  )?.value,
                );
                const birthdayPoint = birthdayRewards.reduce(
                  (sum, reward) => sum + (reward.rewardValue ?? 0),
                  0,
                );
                await updateCustomerPoint(client, customerId, customerPoint, birthdayPoint);
              }

              // Update cursor status
              hasNextPage = customerChunk.data.customers.pageInfo.hasNextPage;
              previousEndCursor = customerChunk.data.customers.pageInfo.endCursor;
            }

            client.stop();
          } catch (error) {
            console.error(`Error processing shop ${shop.myshopifyDomain}:`, error);
          }
        }

        resolve();
      } catch (error) {
        reject(error);
      } finally {
        sem.leave();
      }
    });
  });
}

const getBirthdayRewards = async (shopId: string) =>
  await db.birthdayReward.findMany({
    include: {
      settings: {
        include: {
          shop: true,
        },
      },
    },
    where: {
      settings: {
        shop: {
          shopId: shopId,
        },
      },
    },
  });

const getCustomerChunk = async (
  client: ApolloClient<object>,
  cursor: string | null,
): Promise<CustomerQueryResponse> =>
  await client.query({
    query: gql`
      query ($cursor: String) {
        customers(first: 10, after: $cursor) {
          edges {
            cursor
            node {
              id
              metafields(first: 100) {
                nodes {
                  id
                  namespace
                  key
                  value
                }
              }
            }
          }
          pageInfo {
            hasNextPage
            endCursor
          }
        }
      }
    `,
    variables: {
      cursor: cursor,
    },
  });

const filterBirthdayCustomers = (customers: CustomerQueryResponse) => {
  // Filter customers whose birthday is today
  return customers.data.customers.edges.filter((edge) => {
    const customerBirthday = edge.node.metafields.nodes.find(
      (m) => m.namespace === "custom" && m.key === "birthday",
    )?.value;
    if (!customerBirthday) return false;

    const [, month, day] = customerBirthday.split("-");
    return `${today.getMonth() + 1}-${today.getDate()}` === `${month}-${day}`;
  });
};

const updateCustomerPoint = async (
  client: ApolloClient<object>,
  customerId: string,
  oldPoint: number,
  newPoint: number,
) =>
  await client.mutate({
    mutation: gql`
      mutation updateCustomerMetafields($input: CustomerInput!) {
        customerUpdate(input: $input) {
          userErrors {
            message
            field
          }
        }
      }
    `,
    variables: {
      input: {
        id: customerId,
        metafields: [
          {
            key: "points",
            value: oldPoint + newPoint,
          },
        ],
      },
    },
  });

// ========================================
// DELAYED POINTS JOB (New)
// ========================================

export async function processDelayedPoints() {
  return new Promise<{ processed: number; errors: number }>((resolve, reject) => {
    sem.take(async () => {
      try {
        const now = new Date();
        console.log(`Processing delayed points at ${now.toISOString()}`);

        // Get all due points records
        const dueRecords = await db.loyaltyPointsHistory.findMany({
          where: {
            status: "PENDING",
            issueAt: { lte: now },
          },
          orderBy: {
            issueAt: "asc",
          },
        });

        if (dueRecords.length === 0) {
          console.log("No delayed points to process");
          resolve({ processed: 0, errors: 0 });
          return;
        }

        console.log(`Found ${dueRecords.length} delayed points records to process`);

        // Group records by shop for efficient processing
        const recordsByShop = await groupRecordsByShop(dueRecords);

        let totalProcessed = 0;
        let totalErrors = 0;

        // Process each shop
        for (const [shopDomain, records] of Object.entries(recordsByShop)) {
          try {
            console.log(`Processing ${records.length} records for shop: ${shopDomain}`);

            const shop = await db.shop.findFirst({
              where: { myshopifyDomain: shopDomain },
              select: { shopId: true, myshopifyDomain: true, shopToken: true },
            });

            if (!shop) {
              console.error(` Shop not found: ${shopDomain}`);
              totalErrors += records.length;
              continue;
            }

            const client = await clientByShop(shop);
            const { processed, errors } = await processRecordsForShop(client, records);

            totalProcessed += processed;
            totalErrors += errors;

            client.stop();
          } catch (error) {
            console.error(` Error processing shop ${shopDomain}:`, error);
            totalErrors += records.length;
          }
        }

        console.log(
          ` Delayed points processing completed. Processed: ${totalProcessed}, Errors: ${totalErrors}`,
        );
        resolve({ processed: totalProcessed, errors: totalErrors });
      } catch (error) {
        console.error(" Error in processDelayedPoints:", error);
        reject(error);
      } finally {
        sem.leave();
      }
    });
  });
}

async function groupRecordsByShop(records: any[]) {
  const grouped: { [shopDomain: string]: any[] } = {};

  for (const record of records) {
    // Find shop for this customer (using existing shop records)
    const shops = await db.shop.findMany({
      where: {
        loyaltyPrograms: {
          some: {
            isActive: true,
            programType: "POINTS",
          },
        },
      },
      select: { myshopifyDomain: true },
      take: 1,
    });

    const shopDomain = shops[0]?.myshopifyDomain || "unknown";

    if (!grouped[shopDomain]) {
      grouped[shopDomain] = [];
    }
    grouped[shopDomain].push(record);
  }

  return grouped;
}

async function processRecordsForShop(client: ApolloClient<object>, records: any[]) {
  let processedCount = 0;
  let errorCount = 0;

  for (const record of records) {
    try {
      console.log(`Processing record ${record.id} for customer ${record.customerId}`);

      // Get customer's current points
      const customerData = await client.query({
        query: gql`
          query getCustomerPoints($customerId: ID!) {
            customer(id: $customerId) {
              id
              metafields(first: 10, namespace: "$app") {
                nodes {
                  id
                  namespace
                  key
                  value
                }
              }
            }
          }
        `,
        variables: { customerId: record.customerId },
      });
      const namespace = (await getNamespaceMetafield(client)) as string;
      const customer = customerData.data.customer;
      if (!customer) {
        console.error(` Customer not found: ${record.customerId}`);
        errorCount++;
        await markRecordAsError(record.id);
        continue;
      }

      const pointsMetafield = customer.metafields.nodes.find(
        (m: any) => m.namespace === namespace && m.key === "points",
      );
      const currentPoints = pointsMetafield?.value ? Number(pointsMetafield.value) : 0;
      const newTotal = currentPoints + record.points;

      // Update customer points
      await client.mutate({
        mutation: gql`
          mutation updateCustomerPoints($input: CustomerInput!) {
            customerUpdate(input: $input) {
              customer {
                id
              }
              userErrors {
                field
                message
              }
            }
          }
        `,
        variables: {
          input: {
            id: record.customerId,
            metafields: [
              {
                key: "points",
                value: newTotal.toString(),
                type: "single_line_text_field",
              },
            ],
          },
        },
      });

      // Mark record as issued
      await db.loyaltyPointsHistory.update({
        where: { id: record.id },
        data: {
          status: "ISSUED",
          issuedAt: new Date(),
        },
      });

      processedCount++;
      console.log(
        ` Issued ${record.points} points to customer ${record.customerId}. New total: ${newTotal}`,
      );
    } catch (error) {
      console.error(` Error processing record ${record.id}:`, error);
      errorCount++;
      await markRecordAsError(record.id);
    }
  }

  return { processed: processedCount, errors: errorCount };
}

async function markRecordAsError(recordId: number) {
  try {
    await db.loyaltyPointsHistory.update({
      where: { id: recordId },
      data: { status: "ERROR" },
    });
  } catch (error) {
    console.error(` Error marking record ${recordId} as ERROR:`, error);
  }
}

// ========================================
// CLEANUP JOB (New)
// ========================================

export async function cleanupOldRecords() {
  return new Promise<number>((resolve, reject) => {
    sem.take(async () => {
      try {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - 90); // 90 days old

        const result = await db.loyaltyPointsHistory.deleteMany({
          where: {
            status: "ISSUED",
            issuedAt: {
              lt: cutoffDate,
            },
          },
        });

        console.log(` Cleaned up ${result.count} old ISSUED records older than 90 days`);
        resolve(result.count);
      } catch (error) {
        console.error(" Error in cleanup job:", error);
        reject(error);
      } finally {
        sem.leave();
      }
    });
  });
}

// ========================================
// UTILITY FUNCTIONS
// ========================================

export async function retryFailedPoints() {
  const failedRecords = await db.loyaltyPointsHistory.findMany({
    where: {
      status: "ERROR",
    },
    take: 10,
    orderBy: {
      createdAt: "desc",
    },
  });

  console.log(`Found ${failedRecords.length} failed records to retry`);

  for (const record of failedRecords) {
    await db.loyaltyPointsHistory.update({
      where: { id: record.id },
      data: { status: "PENDING" },
    });
    console.log(`Reset record ${record.id} to PENDING status`);
  }

  return { retried: failedRecords.length };
}

export async function getDelayedPointsStatistics() {
  const stats = await db.loyaltyPointsHistory.groupBy({
    by: ["status"],
    _count: {
      id: true,
    },
    _sum: {
      points: true,
    },
  });

  const totalRecords = await db.loyaltyPointsHistory.count();

  return {
    byStatus: stats,
    totalRecords,
    timestamp: new Date().toISOString(),
  };
}

export async function getNamespaceMetafield(client: any) {
  if (!client) return;
  const appResponse = await client.query({
    query: gql`
      query getApp {
        app {
          id
        }
      }
    `,
  });
  const appId = appResponse.data?.app?.id?.replace("gid://shopify/App/", "");
  const namespace = `app--${appId}`;
  return namespace;
}
