import { Shop } from "@prisma/client";
import db from "../db.server";

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export default async function findShop(admin: any, session: any): Promise<Shop> {
  const shop = await db.shop.findFirst({ where: { myshopifyDomain: session.shop } });
  if (shop) {
    return shop;
  }

  const response = await admin.graphql(`
  #graphql
    query GetShopDetails {
      shop {
        id
        name
        myshopifyDomain
      }
    }
  `);

  const responseJson = await response.json();
  return await db.shop.create({
    data: {
      shopName: responseJson.data.shop.name ?? "",
      shopId: responseJson.data.shop.id ?? "",
      shopToken: session.accessToken,
      myshopifyDomain: responseJson.data.shop.myshopifyDomain,
    },
  });
}
