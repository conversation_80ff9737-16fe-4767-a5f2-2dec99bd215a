import { ApolloClient, InMemoryCache } from "@apollo/client/core";
import { LATEST_API_VERSION } from "@shopify/shopify-app-remix/server";
import db from "./../db.server";

export async function requireAuth(request: Request) {
  return verifyToken(request);
}

export async function verifyToken(request: Request) {
  const authHeader = request.headers.get("Authorization");

  if (!authHeader?.startsWith("Bearer ")) {
    throw new Response(JSON.stringify({ error: "Unauthorized" }), {
      status: 401,
      headers: {
        "Content-Type": "application/json",
      },
    });
  }

  const token = authHeader.split(" ")[1];

  const shopByToken = await db.shop.findFirst({
    where: {
      apiKey: token.trim(),
    },
  });

  if (!shopByToken) {
    throw new Response(JSON.stringify({ error: "Unauthorized" }), {
      status: 401,
      headers: {
        "Content-Type": "application/json",
      },
    });
  }

  const client = await clientByShop(shopByToken);

  return { shopByToken, client };
}

export const clientByShop = async (shop?: { myshopifyDomain: string; shopToken: string | null }) => (
  new ApolloClient({
    uri: `https://${shop?.myshopifyDomain}/admin/api/${LATEST_API_VERSION}/graphql.json`,
    cache: new InMemoryCache(),
    headers: {
      "X-Shopify-Access-Token": shop?.shopToken ?? "",
    },
  })
)
