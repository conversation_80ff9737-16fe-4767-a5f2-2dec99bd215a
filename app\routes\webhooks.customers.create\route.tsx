import db from "@/db.server";
import { authenticate } from "@/shopify.server";
import findShop from "@/utils/find-shop.server";
import { DiscountType, RequirementType, RewardType, WaysEarnRewardType } from "@prisma/client";
import { ActionFunctionArgs } from "@remix-run/node";
import dayjs from "dayjs";
import { generateRandomCode } from "../../utils/helper";

export const action = async ({ request }: ActionFunctionArgs) => {
  const { payload, topic, shop, admin, session } = await authenticate.webhook(request);
  console.log(`Received ${topic} webhook for ${shop}`);

  const ownerId = payload.admin_graphql_api_id;

  /**
   * 1. Get shop
   * 2. Get config in @shema
   * 3. Check metafields base on config
   * 4. Update metafields base on config
   */

  const shopId = (await findShop(admin, session)).id;

  const wayToEarn = await db.waysEarnReward.findFirst({
    where: {
      shopId,
      typeEarnReward: WaysEarnRewardType.SIGN_UP,
      isActive: true,
    },
    include: {
      rewards: true,
    },
  });

  if (!wayToEarn) return new Response();

  for (const reward of wayToEarn.rewards) {
    switch (reward.rewardType) {
      case RewardType.POINTS:
        await admin?.graphql(
          `
        #graphql
          mutation MetafieldsSet($metafields: [MetafieldsSetInput!]!) {
            metafieldsSet(metafields: $metafields) {
              metafields {
                key
                namespace
                value
                createdAt
                updatedAt
              }
              userErrors {
                field
                message
                code
              }
            }
          }
          `,
          {
            variables: {
              metafields: [
                {
                  key: "points",
                  namespace: "$app",
                  ownerId,
                  type: "number_integer",
                  value: reward.value,
                },
              ],
            },
          },
        );
        break;
      case RewardType.STORE_CREDIT:
        await admin?.graphql(
          `
        #graphql
          mutation MetafieldsSet($metafields: [MetafieldsSetInput!]!) {
            metafieldsSet(metafields: $metafields) {
              metafields {
                key
                namespace
                value
                createdAt
                updatedAt
              }
              userErrors {
                field
                message
                code
              }
            }
          }
          `,
          {
            variables: {
              metafields: [
                {
                  key: "store_credit",
                  namespace: "$app",
                  ownerId,
                  type: "number_decimal",
                  value: reward.value,
                },
              ],
            },
          },
        );
        break;
      case RewardType.AMOUNT_OFF:
        {
          const discountCode = "AMOUNT_OFF_" + generateRandomCode();

          const basicCodeDiscount = {
            code: discountCode,
            title: discountCode,
            customerSelection: {
              customers: {
                add: [ownerId],
              },
            },
            customerGets: {
              value:
                reward.discountType === DiscountType.PERCENTAGE
                  ? { percentage: reward.value }
                  : { discountAmount: { amount: reward.value } },
              items: { all: true },
            },
            minimumRequirement:
              reward.minimumRequirement === RequirementType.AMOUNT
                ? { subtotalRange: { greaterThanOrEqualToSubtotal: reward.minimumValue } }
                : reward.minimumRequirement === RequirementType.QUANTITY
                  ? { quantityRange: { greaterThanOrEqualToQuantity: reward.minimumValue } }
                  : {},
            usageLimit: 1,
            appliesOncePerCustomer: true,
            startsAt: dayjs().toISOString(),
          };

          await admin
            ?.graphql(
              `
          #graphql
          mutation CreateDiscountCode($basicCodeDiscount: DiscountCodeBasicInput!) {
            discountCodeBasicCreate(basicCodeDiscount: $basicCodeDiscount) {
              codeDiscountNode {
                id
              }
              userErrors {
                field
                message
              }
            }
          }
          `,
              {
                variables: {
                  basicCodeDiscount,
                },
              },
            )
            .catch((error) => {
              console.error("Error creating discount code: ", error);
            });
        }

        break;
      case RewardType.FREE_SHIPPING:
        {
          const discountCode = "FREE_SHIP_" + generateRandomCode();
          await admin?.graphql(
            `
          #graphql
          mutation CreateFreeShippingCode($input: DiscountCodeFreeShippingInput!) {
            discountCodeFreeShippingCreate(freeShippingCodeDiscount: $input) {
              codeDiscountNode {
                id
              }
              userErrors {
                field
                message
              }
            }
          }
          `,
            {
              variables: {
                input: {
                  code: discountCode,
                  title: discountCode,
                  appliesOncePerCustomer: true,
                  usageLimit: 1,
                  customerSelection: {
                    customers: {
                      add: [ownerId],
                    },
                  },
                  startsAt: dayjs().toISOString(),
                },
              },
            },
          );
        }

        break;
      default:
        break;
    }
  }

  return new Response();
};
