import { gql } from "@apollo/client/core";

interface MoneyV2 {
  amount: string;
  currencyCode: string;
}

interface MetafieldNode {
  namespace: string;
  key: string;
  type: string;
  value: string;
}

export const queryMemberFromCellphone = gql`
  query ($identifier: CustomerIdentifierInput!) {
    customerByIdentifier(identifier: $identifier) {
      id
      phone
      email
    }
  }
`;

export const queryDiscountByCode = gql`
  query codeDiscountNodeByCode($code: String!) {
    codeDiscountNodeByCode(code: $code) {
      id
      codeDiscount {
        __typename
        ... on DiscountCodeBasic {
          customerGets {
            value {
              __typename
              ... on DiscountOnQuantity {
                effect {
                  __typename
                }
              }
            }
          }
        }
        ... on DiscountCodeBxgy {
          customerGets {
            value {
              __typename
              ... on DiscountOnQuantity {
                effect {
                  __typename
                }
              }
            }
          }
        }
      }
    }
  }
`;

export const queryOrderWithLineItems = gql`
  query OrderWithLineItems($id: ID!) {
    order(id: $id) {
      id
      lineItems(first: 100) {
        nodes {
          id
          variant {
            id
          }
          sku
        }
      }
    }
  }
`

export interface QueryOrderWithLineItemsInput {
  id: string;
}

export interface QueryOrderWithLineItemsResponse {
  order: {
    id: string;
    lineItems: {
      nodes: {
        id: string;
        variant: {
          id: string;
        };
        sku: string;
      }[];
    };
  };
}

export const queryOrderWithSuggestedRefund = gql`
  query SuggestedRefund($id: ID!, $refundLineItems: [RefundLineItemInput!]) {
    order(id: $id) {
      id
      customer {
        id
      }
      discountCodes
      suggestedRefund(refundLineItems: $refundLineItems) {
        refundLineItems {
          lineItem {
            id
            variant {
              id
            }
            sku
            title
            currentQuantity
            originalUnitPriceSet {
              shopMoney {
                amount
                currencyCode
              }
            }
            totalDiscountSet {
              shopMoney {
                amount
                currencyCode
              }
            }
            discountedTotalSet {
              shopMoney {
                amount
                currencyCode
              }
            }
          }
          quantity
        }
        subtotalSet {
          shopMoney {
            amount
            currencyCode
          }
        }
        discountedSubtotalSet {
          shopMoney {
            amount
            currencyCode
          }
        }
        amountSet {
          shopMoney {
            amount
            currencyCode
          }
        }
      }
      metafields(first: 10) {
        nodes {
          namespace
          key
          type
          value
        }
      }
    }
  }
`

export interface QueryOrderWithSuggestedRefundInput {
  id: string;
  refundLineItems: {
    lineItemId: string;
    quantity: number;
  }[];
}

export interface QueryOrderWithSuggestedRefundResponse {
  order: {
    id: string;
    customer?: {
      id: string;
    }
    discountCodes: string[];
    suggestedRefund: {
      refundLineItems: {
        lineItem: {
          id: string;
          variant: {
            id: string;
          };
          sku: string;
          title: string;
          currentQuantity: number;
          originalUnitPriceSet: {
            shopMoney: MoneyV2;
          };
          totalDiscountSet: {
            shopMoney: MoneyV2;
          };
          discountedTotalSet: {
            shopMoney: MoneyV2;
          };
        };
        quantity: number;
      }[];
      subtotalSet: {
        shopMoney: MoneyV2;
      };
      discountedSubtotalSet: {
        shopMoney: MoneyV2;
      };
      amountSet: {
        shopMoney: MoneyV2;
      };
    };
    metafields: {
      nodes: MetafieldNode[];
    };
  };
}
