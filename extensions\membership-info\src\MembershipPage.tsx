import {
  Banner,
  <PERSON>Stack,
  <PERSON><PERSON>,
  Page,
  Text,
  View,
  reactExtension,
  useApi,
} from "@shopify/ui-extensions-react/customer-account";
import { useEffect, useState } from "react";
import { MembershipData } from "../types/membershipTypes";
import { fetchCustomerData } from "../services/membershipService";
// Fix import path - remove file extensions
import { ActivityTimeline } from "./components/ActivityTimeline";
import { MemberInfoCard } from "./components/MemberInfoCard";

export default reactExtension("customer-account.page.render", () => <MembershipDashboard />);

function MembershipDashboard() {
  const { authenticatedAccount } = useApi();
  const [isLoading, setIsLoading] = useState(true);
  const [data, setData] = useState<MembershipData | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);
        console.log("Loading with authenticated account:", authenticatedAccount);
        // Fetch membership data from API
        const membershipData = await fetchCustomerData();
        console.log("Data loaded successfully:", membershipData);
        setData(membershipData);
        setError(null);
      } catch (err) {
        console.error("Error in component:", err);
        setError("Unable to load membership data. Please try again later.");
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [authenticatedAccount]);

  // Check component loading
  if (isLoading) {
    return (
      <Page title="Membership">
        <BlockStack spacing="loose">
          <View inlineAlignment="center" padding="base">
            <Text>Loading membership data...</Text>
          </View>
        </BlockStack>
      </Page>
    );
  }

  // Check error state
  if (error) {
    return (
      <Page title="Membership">
        <BlockStack spacing="loose">
          <Banner status="critical">{error}</Banner>
          <Button onPress={() => window.location.reload()}>Try Again</Button>
        </BlockStack>
      </Page>
    );
  }

  // Check if data is loaded
  if (!data) {
    return (
      <Page title="Membership">
        <BlockStack spacing="loose">
          <Text>No membership data available.</Text>
        </BlockStack>
      </Page>
    );
  }

  // Return the components
  return (
    <Page title="Membership">
      <BlockStack spacing="loose">
        {/* Display member information card */}
        <MemberInfoCard customer={data.customer} membership={data.membership} />

        {/* Display activity timeline */}
        <ActivityTimeline activities={data.activities} />
      </BlockStack>
    </Page>
  );
}
