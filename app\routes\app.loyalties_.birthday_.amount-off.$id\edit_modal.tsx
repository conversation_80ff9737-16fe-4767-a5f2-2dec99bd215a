import { Modal, TitleBar, useAppBridge } from "@shopify/app-bridge-react";
import {
  BlockStack,
  Box,
  Button,
  ButtonGroup,
  Card,
  Checkbox,
  FormLayout,
  InlineStack,
  Layout,
  RadioButton,
  Text,
  TextField,
} from "@shopify/polaris";
import { Dispatch, SetStateAction, useCallback } from "react";
import { useTranslation } from "react-i18next";

interface EditAmountOffModalProps {
  id: string;
  isOpen?: boolean;
  rewardValue: number;
  setRewardValue: Dispatch<SetStateAction<number>>;
  discountType: "percentage" | "fixed";
  setDiscountType: Dispatch<SetStateAction<"percentage" | "fixed">>;
  minimumRequirement: "none" | "amount" | "quantity";
  setMinimumRequirement: Dispatch<SetStateAction<"none" | "amount" | "quantity">>;
  minimumValue: number | null;
  setMinimumValue: Dispatch<SetStateAction<number | null>>;
  combinations: {
    product: boolean;
    order: boolean;
    shipping: boolean;
  };
  setCombinations: Dispatch<
    SetStateAction<{
      product: boolean;
      order: boolean;
      shipping: boolean;
    }>
  >;
  setHasDiscount?: Dispatch<SetStateAction<boolean>>;
}

export default function EditAmountOffModal({
  id,
  isOpen,
  rewardValue,
  setRewardValue,
  discountType,
  setDiscountType,
  minimumRequirement,
  setMinimumRequirement,
  minimumValue,
  setMinimumValue,
  combinations,
  setCombinations,
  setHasDiscount,
}: EditAmountOffModalProps) {
  const shopify = useAppBridge();
  const { t } = useTranslation();

  const handleClose = useCallback(() => {
    shopify?.modal?.hide(id);
  }, [shopify, id]);

  const handleSaveAndClose = useCallback(() => {
    setHasDiscount?.(true);
    handleClose();
  }, [setHasDiscount, handleClose]);

  const handleDeleteAndClose = useCallback(() => {
    setHasDiscount?.(false);
    handleClose();
  }, [setHasDiscount, handleClose]);

  return (
    <Modal id={id} open={isOpen} onHide={handleClose}>
      <Box padding="400">
        <Layout>
          {/* Value Block */}
          <Layout.Section>
            <Card>
              <BlockStack gap="200">
                <Text as="h2" variant="headingMd">
                  {t("birthday.amountOff.value")}
                </Text>
                <FormLayout>
                  <InlineStack gap="200" blockAlign="start" align="space-between">
                    <ButtonGroup gap="extraTight">
                      <Button
                        pressed={discountType === "percentage"}
                        onClick={() => setDiscountType("percentage")}
                      >
                        {t("birthday.amountOff.percentage")}
                      </Button>
                      <Button
                        pressed={discountType === "fixed"}
                        onClick={() => setDiscountType("fixed")}
                      >
                        {t("birthday.amountOff.fixed")}
                      </Button>
                    </ButtonGroup>
                    <InlineStack gap="100" blockAlign="center" align="center">
                      {discountType === "fixed" && <Text as="p">$</Text>}
                      <TextField
                        label=""
                        value={rewardValue.toString()}
                        onChange={(value) => setRewardValue(Number(value))}
                        type="number"
                        autoComplete="off"
                      />
                      {discountType === "percentage" && <Text as="p">%</Text>}
                    </InlineStack>
                  </InlineStack>
                </FormLayout>
              </BlockStack>
            </Card>
          </Layout.Section>

          {/* Minimum purchase requirements */}
          <Layout.Section>
            <Card>
              <BlockStack gap="200">
                <Text as="h2" variant="headingMd">
                  {t("birthday.amountOff.minimumRequirements")}
                </Text>
                <FormLayout>
                  <BlockStack gap="100">
                    <RadioButton
                      label="No minimum requirements"
                      checked={minimumRequirement === "none"}
                      id="none"
                      name="minRequirement"
                      onChange={() => setMinimumRequirement("none")}
                    />
                    <RadioButton
                      label="Minimum purchase amount ($)"
                      checked={minimumRequirement === "amount"}
                      id="amount"
                      name="minRequirement"
                      onChange={() => {
                        setMinimumRequirement("amount");
                        if (minimumRequirement !== "amount") setMinimumValue(null);
                      }}
                    />
                    {minimumRequirement === "amount" && (
                      <Box paddingInlineStart="300" maxWidth="50%">
                        <TextField
                          label=""
                          type="number"
                          min={0}
                          value={minimumValue !== null ? minimumValue.toString() : ""}
                          onChange={(value) => setMinimumValue(value === "" ? null : Number(value))}
                          autoComplete="off"
                          placeholder="Enter amount"
                        />
                      </Box>
                    )}
                    <RadioButton
                      label="Minimum quantity of items"
                      checked={minimumRequirement === "quantity"}
                      id="quantity"
                      name="minRequirement"
                      onChange={() => {
                        setMinimumRequirement("quantity");
                        if (minimumRequirement !== "quantity") setMinimumValue(null);
                      }}
                    />
                    {minimumRequirement === "quantity" && (
                      <Box paddingInlineStart="600" maxWidth="50%">
                        <TextField
                          label=""
                          type="number"
                          min={0}
                          value={minimumValue !== null ? minimumValue.toString() : ""}
                          onChange={(value) => setMinimumValue(value === "" ? null : Number(value))}
                          autoComplete="off"
                          placeholder="Enter quantity"
                        />
                      </Box>
                    )}
                  </BlockStack>
                </FormLayout>
              </BlockStack>
            </Card>
          </Layout.Section>

          {/* Combinations */}
          <Layout.Section>
            <Card>
              <BlockStack gap="200">
                <Text as="h2" variant="headingMd">
                  {t("birthday.amountOff.combinations")}
                </Text>
                <Text as="p" tone="subdued">
                  {t("birthday.amountOff.combinationsDescription")}
                </Text>
                <FormLayout>
                  <BlockStack gap="100">
                    <Checkbox
                      label={t("birthday.amountOff.discount.product")}
                      checked={combinations.product}
                      onChange={(value) => setCombinations((c) => ({ ...c, product: value }))}
                    />
                    <Checkbox
                      label={t("birthday.amountOff.discount.order")}
                      checked={combinations.order}
                      onChange={(value) => setCombinations((c) => ({ ...c, order: value }))}
                    />
                    <Checkbox
                      label={t("birthday.amountOff.discount.shipping")}
                      checked={combinations.shipping}
                      onChange={(value) => setCombinations((c) => ({ ...c, shipping: value }))}
                    />
                  </BlockStack>
                </FormLayout>
              </BlockStack>
            </Card>
          </Layout.Section>

          <Layout.Section>
            <InlineStack gap="600" align="space-between">
              <Button size="large" variant="primary" tone="critical" onClick={handleDeleteAndClose}>
                {t("common.delete")}
              </Button>
              <Button size="large" variant="primary" onClick={handleSaveAndClose}>
                {t("common.save")}
              </Button>
            </InlineStack>
          </Layout.Section>
        </Layout>
      </Box>

      <TitleBar title={t("birthday.form.amountOff.title")} />
    </Modal>
  );
}
