import { LoaderFunctionArgs, json } from "@remix-run/node";
import { MemberEvent, MemberVipTier } from "../../constants";
import { authenticate } from "../../shopify.server";
import { getNamespaceMetafield } from "../webhooks.returns.close/services";

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const { admin, session } = await authenticate.admin(request);
  const { id } = params;

  if (!id) return json({ notFound: true });

  try {
    const shopifyCustomerId = id.includes("gid:") ? id : `gid://shopify/Customer/${id}`;

    const response = await admin.graphql(
      `query getCustomer($id: ID!) {
        customer(id: $id) {
          id
          legacyResourceId
          displayName
          email
          phone
          firstName
          lastName
          numberOfOrders
          amountSpent {amount currencyCode}
          addresses(first: 1) {address1 city country}
          orders(first: 50, sortKey: PROCESSED_AT, reverse: true) {
            edges {
              node {
                id
                legacyResourceId
                name
                processedAt
                displayFinancialStatus
                displayFulfillmentStatus
                closed
                cancelledAt
                totalPriceSet {shopMoney {amount currencyCode}}
                subtotalPriceSet {shopMoney {amount currencyCode}}
                totalDiscountsSet {shopMoney {amount currencyCode}}
                fullyPaid
                transactions(first: 10) {
                  id
                  kind
                  status
                  amountSet {
                    shopMoney {amount currencyCode}
                  }
                  processedAt
                }
                refunds(first: 5) {
                  id
                  createdAt
                  totalRefundedSet {
                    shopMoney {amount currencyCode}
                  }
                }
              }
            }
          }
          metafields(first: 20) {
            edges {
              node {
                namespace
                key
                value
                createdAt
                updatedAt
              }
            }
          }
        }
      }`,
      { variables: { id: shopifyCustomerId } },
    );

    const responseJson = await response.json();

    if (!responseJson.data?.customer) return json({ notFound: true });

    const customer = responseJson.data.customer;

    const namespace = (await getNamespaceMetafield(admin)) as string;
    const metafields = customer.metafields?.edges.reduce((acc: any, { node }: any) => {
      if (node.namespace === namespace && node.key === "points") acc.points = node.value;
      if (node.namespace === namespace && node.key === "vip_tier") acc.vipTier = node.value;
      if (node.key === "birth_date" || node.key === "birthday") acc.birthday = node.value;
      if (node.key === "gender") acc.gender = node.value;
      if (node.key === "register_date") acc.registerDate = node.value;
      if (node.key === "register_employee") acc.registerEmployee = node.value;
      if (node.key === "register_location") acc.registerLocation = node.value;
      return acc;
    }, {});

    const allOrders =
      customer.orders?.edges.map(({ node: order }: any) => ({
        id: order.id,
        numericId: order.legacyResourceId,
        name: order.name,
        processedAt: order.processedAt,
        displayFinancialStatus: order.displayFinancialStatus,
        fullyPaid: order.fullyPaid,
        closed: order.closed,
        cancelledAt: order.cancelledAt,
        totalPrice: {
          amount: parseFloat(order.totalPriceSet?.shopMoney?.amount || 0),
          currencyCode: order.totalPriceSet?.shopMoney?.currencyCode,
        },
        subtotalPrice: {
          amount: parseFloat(order.subtotalPriceSet?.shopMoney?.amount || 0),
          currencyCode: order.subtotalPriceSet?.shopMoney?.currencyCode,
        },
        totalDiscounts: {
          amount: parseFloat(order.totalDiscountsSet?.shopMoney?.amount || 0),
          currencyCode: order.totalDiscountsSet?.shopMoney?.currencyCode,
        },
        transactions:
          order.transactions?.map((transaction: any) => ({
            id: transaction.id,
            kind: transaction.kind,
            status: transaction.status,
            amount: parseFloat(transaction.amountSet?.shopMoney?.amount || 0),
            currencyCode: transaction.amountSet?.shopMoney?.currencyCode,
            processedAt: transaction.processedAt,
            giftCard: transaction.giftCard || null,
          })) || [],
        refunds:
          order.refunds?.map((refund: any) => ({
            id: refund.id,
            createdAt: refund.createdAt,
            amount: parseFloat(refund.totalRefundedSet?.shopMoney?.amount || 0),
            currencyCode: refund.totalRefundedSet?.shopMoney?.currencyCode,
          })) || [],
      })) || [];

    // For now, we'll use the Shopify lifetime values directly
    const ordersCount = customer.numberOfOrders || 0;
    const totalSpent = parseFloat(customer.amountSpent?.amount || "0");

    // Generate timeline events based on orders
    const events: MemberEvent[] = [];

    allOrders.forEach((order) => {
      events.push({
        id: `order-${order.id}`,
        type: "order_placed",
        description: `This customer placed an order ${order.name}.`,
        timestamp: order.processedAt,
        data: {
          orderId: order.name,
          orderNumericId: order.numericId,
        },
      });

      const paymentTransaction = order.transactions?.find(
        (t: any) => t.kind === "SALE" && t.status === "SUCCESS",
      );

      if (paymentTransaction) {
        events.push({
          id: `payment-${order.id}`,
          type: "order_payment",
          description: `This customer completed the payment for order ${order.name}.`,
          timestamp: paymentTransaction.processedAt || order.processedAt,
          data: {
            orderId: order.name,
            orderNumericId: order.numericId,
          },
        });
      }

      order.refunds?.forEach((refund: any, index: number) => {
        events.push({
          id: `refund-${order.id}-${index}`,
          type: "refund",
          description: `Refund processed for order ${order.name}.`,
          timestamp: refund.createdAt,
          data: {
            orderId: order.name,
            orderNumericId: order.numericId,
            amount: refund.amount,
          },
        });
      });
    });

    // Sort events by timestamp (newest first)
    events.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

    const address = customer.addresses?.[0]
      ? `${customer.addresses[0].address1 ?? ""}, ${customer.addresses[0].city ?? ""}, ${
          customer.addresses[0].country ?? ""
        }`
          .replace(/,,/g, ",")
          .replace(/^,|,$/g, "")
      : "";

    const numericId = customer.legacyResourceId ?? customer.id.split("/").pop();

    const defaultCurrency = session.currency?.isoCode ?? "TWD";

    const shopDomain = session.shop;

    return json({
      member: {
        id: customer.id,
        numericId,
        fullName:
          customer.displayName ??
          `${customer.firstName ?? ""}${customer.lastName ? " " + customer.lastName : ""}`.trim() ??
          customer.email,
        email: customer.email ?? "",
        phone: customer.phone ?? "",
        points: metafields?.points ?? null,
        vipTier: (metafields?.vipTier as MemberVipTier) ?? MemberVipTier.STARTER,
        ordersCount: ordersCount,
        totalSpent: {
          amount: totalSpent,
          currencyCode: defaultCurrency,
        },
        lifetimeOrders: customer.numberOfOrders ?? 0,
        lifetimeSpent: customer.amountSpent ?? { amount: 0, currencyCode: defaultCurrency },
        address,
        birthday: metafields?.birthday,
        gender: metafields?.gender,
        registerDate: metafields?.registerDate,
        registerEmployee: metafields?.registerEmployee,
        registerLocation: metafields?.registerLocation,
        socialLinks: { facebook: "", google: "", line: "" },
        events,
      },
      notFound: false,
      shop: shopDomain,
    });
  } catch (error) {
    console.error("Error fetching customer:", error);
    return json({ notFound: true, error: String(error) });
  }
};
