import { Link } from "@remix-run/react";
import { Button, Image } from "@shopify/polaris";

interface FeatureCardProps {
  title: string;
  description: string;
  buttonText: string;
  disabled?: boolean;
  to?: string;
}

interface FeatureTabProps {
  card: FeatureCardProps;
  url: string;
}

export default function FeatureTab({ card, url }: Readonly<FeatureTabProps>) {
  return (
    <div className="p-6 bg-white flex flex-col h-full rounded-lg">
      <div className="flex-grow">
        <div className="aspect-square relative overflow-hidden mb-4 max-w-[240px] mx-auto">
          <Image source={url} alt={card.title} className="object-contain" />
        </div>
        <h3 className="text-lg font-semibold mb-2">{card.title}</h3>
        <p className="text-gray-600 mb-6 text-sm">{card.description}</p>
      </div>
      <div className="mt-auto">
        <Link to={card.to || "#"} className="w-full">
          <Button fullWidth variant="primary" disabled={card.disabled}>
            {card.buttonText}
          </Button>
        </Link>
      </div>
    </div>
  );
}
