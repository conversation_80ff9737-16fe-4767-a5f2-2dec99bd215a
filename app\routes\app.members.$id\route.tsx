import { useLoaderData, useNavigate as useRemixNavigate, useSubmit } from "@remix-run/react";
import {
  Ava<PERSON>,
  Badge,
  BlockStack,
  Box,
  Button,
  Card,
  DatePicker,
  Divider,
  FormLayout,
  Grid,
  InlineStack,
  Modal,
  Page,
  Link as PolarisLink,
  Text,
  TextField,
  type Range,
} from "@shopify/polaris";
import { ViewIcon } from "@shopify/polaris-icons";
import { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { MemberEvent } from "../../constants";
import { action } from "./action";
import { loader } from "./loader";

export { action, loader };

// Add i18n namespace
export const handle = {
  i18n: "translation",
};

export default function MemberDetailsPage() {
  const data = useLoaderData<typeof loader>();
  const remixNavigate = useRemixNavigate();
  const { t } = useTranslation();
  const [shopName, setShopName] = useState<string | null>(null);
  const [birthdayModalOpen, setBirthdayModalOpen] = useState(false);
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [tempSelectedDate, setTempSelectedDate] = useState<Date | null>(null);
  const [month, setMonth] = useState(1);
  const [year, setYear] = useState(2000);
  const submit = useSubmit();

  const formatDateToYYYYMMDD = (date: Date | null) => {
    if (!date || isNaN(date.getTime())) return "";
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    return `${year}-${month}-${day}`;
  };

  useEffect(() => {
    if (data.shop) {
      const shop = data.shop.split(".")[0];
      setShopName(shop);
    }
  }, [data.shop]);

  useEffect(() => {
    if (data.member?.birthday) {
      try {
        const birthdayDate = new Date(data.member.birthday);
        if (!isNaN(birthdayDate.getTime())) {
          setSelectedDate(birthdayDate);
          setTempSelectedDate(birthdayDate);
          setMonth(birthdayDate.getMonth());
          setYear(birthdayDate.getFullYear());
        }
      } catch (e) {
        console.error("Invalid date format:", e);
      }
    }
  }, [data.member?.birthday]);

  const handleBirthdayEdit = () => {
    setBirthdayModalOpen(true);
    setTempSelectedDate(selectedDate);
  };

  const handleCloseBirthdayModal = () => {
    setBirthdayModalOpen(false);
  };

  const handleSubmitBirthday = () => {
    if (tempSelectedDate && !isNaN(tempSelectedDate.getTime())) {
      setSelectedDate(tempSelectedDate);
      const formattedDate = formatDateToYYYYMMDD(tempSelectedDate);
      const formData = new FormData();
      formData.append("birthday", formattedDate);
      submit(formData, {
        method: "post",
        replace: true,
      });
      handleCloseBirthdayModal();
    }
  };

  const handleMonthChange = useCallback((month: number) => setMonth(month), []);
  const handleYearChange = useCallback((year: number) => setYear(year), []);
  const handleDateChange = useCallback((dateRange: Range) => {
    const selected = dateRange.start;
    if (selected && !isNaN(selected.getTime())) {
      setTempSelectedDate(selected);
    }
  }, []);

  if (!data) return null;

  if (data.notFound || !data.member) {
    return (
      <Page>
        <Card>
          <BlockStack align="center">
            <Text variant="headingLg" as="h2">
              {t("members.details.notFound")}
            </Text>
            <Text as="p">{t("members.details.notFoundMessage")}</Text>
            <Button onClick={() => remixNavigate("/app/members")}>
              {t("members.details.backToList")}
            </Button>
            {data.error && (
              <Text tone="critical" as="p">
                {t("members.details.errorPrefix")}
                {data.error}
              </Text>
            )}
          </BlockStack>
        </Card>
      </Page>
    );
  }

  const { member } = data;

  const ordersCount = Number(member.ordersCount) ?? 0;
  const totalAmount = Number(member.totalSpent.amount) ?? 0;
  const lifetimeAmount = Number(member.lifetimeSpent.amount) ?? 0;

  const formatTime = (isoDate: string) =>
    new Date(isoDate).toLocaleTimeString(t("locale") || "en-US", {
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    });

  const formatDate = (isoDate: string) => {
    const date = new Date(isoDate);
    return `${date.toLocaleString(t("locale") || "en-US", { month: "short" })} ${date.getDate()}`;
  };

  const formatBirthday = (isoDate: string | undefined) => {
    if (!isoDate) return t("members.details.profile.notSet");
    try {
      const date = new Date(isoDate);
      if (isNaN(date.getTime())) return t("members.details.profile.invalidDate");
      return date.toLocaleDateString(t("locale") || "en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
      });
    } catch (e) {
      return t("members.details.profile.invalidDate");
    }
  };

  const groupedEvents = member.events.reduce((acc: Record<string, MemberEvent[]>, event) => {
    const date = formatDate(event.timestamp);
    if (date) {
      if (!acc[date]) {
        acc[date] = [];
      }
      acc[date].push(event);
    }
    return acc;
  }, {});

  const handleViewInShopify = () => {
    if (shopName) {
      const shopifyAdminUrl = `https://${shopName}.myshopify.com/admin/customers/${member.numericId}`;
      window.open(shopifyAdminUrl, "_top");
    } else {
      console.error("Shop name not available");
    }
  };

  const getOrderUrl = (orderNumericId: string) => {
    if (shopName) {
      return `https://${shopName}.myshopify.com/admin/orders/${orderNumericId}`;
    }
    return "#";
  };

  const handleOrderClick = (orderNumericId: string) => {
    if (shopName) {
      const orderUrl = `https://${shopName}.myshopify.com/admin/orders/${orderNumericId}`;
      window.open(orderUrl, "_top");
    }
  };

  return (
    <Page
      fullWidth
      backAction={{ content: t("navigation.members"), url: "/app/members" }}
      title={member.fullName}
      secondaryActions={[
        {
          content: t("members.details.viewInShopify"),
          icon: ViewIcon,
          onAction: handleViewInShopify,
        },
      ]}
    >
      <Grid columns={{ xs: 1, lg: 12 }} gap="8">
        <Grid.Cell columnSpan={{ xs: 12, lg: 9 }}>
          <BlockStack gap="400">
            <Grid columns={{ xs: 1, lg: 2 }} gap="8" data-id="stats-grid">
              {[
                {
                  title: t("members.details.stats.ordersCount"),
                  value: ordersCount.toLocaleString(),
                },
                {
                  title: t("members.details.stats.totalSpent"),
                  value: `${member.totalSpent.currencyCode} ${totalAmount.toFixed(2)}`,
                },
                {
                  title: t("members.details.stats.lifetimeOrders"),
                  value: member.lifetimeOrders.toLocaleString(),
                },
                {
                  title: t("members.details.stats.lifetimeSpent"),
                  value: `${member.lifetimeSpent.currencyCode} ${lifetimeAmount.toFixed(2)}`,
                },
              ].map(({ title, value }) => (
                <Grid.Cell key={title} columnSpan={{ xs: 12, lg: 1 }}>
                  <Card>
                    <Box padding="4">
                      <BlockStack>
                        <Text
                          variant="bodySm"
                          color="subdued"
                          fontWeight="bold"
                          className="text-center"
                        >
                          {title}
                        </Text>
                        <Text variant="headingLg" fontWeight="bold">
                          {value}
                        </Text>
                      </BlockStack>
                    </Box>
                  </Card>
                </Grid.Cell>
              ))}
            </Grid>
            <Card data-id="timeline-grid">
              <Box padding="5">
                <BlockStack>
                  <Text variant="headingMd" fontWeight="bold" as="h3">
                    {t("members.details.timeline.title")}
                  </Text>
                  <Divider />
                  <BlockStack>
                    {Object.entries(groupedEvents).length > 0 ? (
                      Object.entries(groupedEvents).map(([date, events]) => (
                        <div key={date} style={{ marginBottom: "16px" }}>
                          <div
                            style={{
                              display: "flex",
                              justifyContent: "flex-start",
                              alignItems: "center",
                              marginBottom: "8px",
                            }}
                          >
                            <Text variant="bodyMd" fontWeight="semibold" as="p" tone="subdued">
                              {date}
                            </Text>
                          </div>
                          {events.map((event, index) => (
                            <div
                              key={event.id}
                              style={{
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "flex-start",
                                marginLeft: "20px",
                                marginTop: "4px",
                                marginBottom: "4px",
                                position: "relative",
                              }}
                            >
                              <div
                                style={{
                                  position: "absolute",
                                  left: "-12px",
                                  width: "10px",
                                  height: "10px",
                                  borderRadius: "50%",
                                  backgroundColor: "#E0E0E0",
                                }}
                              />
                              {index < events.length - 1 && (
                                <div
                                  style={{
                                    position: "absolute",
                                    left: "-8px",
                                    top: "12px",
                                    width: "2px",
                                    height: "100%",
                                    backgroundColor: "#E0E0E0",
                                  }}
                                />
                              )}
                              <div
                                style={{
                                  display: "flex",
                                  justifyContent: "space-between",
                                  alignItems: "center",
                                  width: "100%",
                                  marginLeft: "14px",
                                }}
                              >
                                <Text variant="bodyMd">
                                  {event.type.includes("order") && event.data?.orderNumericId ? (
                                    <>
                                      {event.description.split(event.data.orderId)[0]}
                                      <PolarisLink
                                        url={getOrderUrl(event.data.orderNumericId)}
                                        monochrome
                                        onClick={() => handleOrderClick(event.data.orderNumericId)}
                                        style={{ color: "rgb(42, 94, 146)" }}
                                      >
                                        {event.data.orderId}
                                      </PolarisLink>
                                      {event.description.split(event.data.orderId)[1] || ""}
                                    </>
                                  ) : (
                                    event.description
                                  )}
                                </Text>
                                <Text variant="bodySm" color="subdued">
                                  {formatTime(event.timestamp)}
                                </Text>
                              </div>
                            </div>
                          ))}
                        </div>
                      ))
                    ) : (
                      <Text variant="bodyMd" tone="subdued" alignment="center" as="p">
                        {t("members.details.timeline.noEvents")}
                      </Text>
                    )}
                  </BlockStack>
                </BlockStack>
              </Box>
            </Card>
          </BlockStack>
        </Grid.Cell>
        <Grid.Cell columnSpan={{ xs: 12, lg: 3 }}>
          <BlockStack gap="400">
            <Card data-id="profile-grid">
              <BlockStack>
                <Box padding="4">
                  <Text variant="headingMd" fontWeight="bold" alignment="center">
                    {member.fullName}
                  </Text>
                  <Box paddingBlockStart="2">
                    <BlockStack align="center">
                      <Avatar size="lg" name={member.fullName} customer />
                    </BlockStack>
                  </Box>
                </Box>
                <Divider />
                <Box padding="4">
                  <BlockStack gap="4">
                    <Box>
                      <Text variant="bodyMd" fontWeight="bold" as="h3">
                        {t("members.details.profile.contactInfo")}
                      </Text>
                      <Box paddingBlockStart="2">
                        <Box paddingBlockEnd="2">
                          <Text variant="bodyMd" as="p">
                            <Text fontWeight="semibold" as="span">
                              {t("members.details.profile.phone")}
                            </Text>
                            {member.phone ?? "N/A"}
                          </Text>
                        </Box>
                        <Box paddingBlockEnd="2">
                          <Text variant="bodyMd" as="p">
                            <Text fontWeight="semibold" as="span">
                              {t("members.details.profile.email")}
                            </Text>
                            {member.email ?? "N/A"}
                          </Text>
                        </Box>
                      </Box>
                    </Box>
                    <Box>
                      <Text variant="bodyMd" fontWeight="bold" as="h3">
                        {t("members.details.profile.address")}
                      </Text>
                      <Box paddingBlockStart="2">
                        <Text variant="bodyMd" as="p">
                          {member.address || t("members.details.profile.notSet")}
                        </Text>
                      </Box>
                    </Box>
                    {(member.gender || member.registerDate) && (
                      <Box>
                        <Text variant="bodyMd" fontWeight="bold" as="h3">
                          {t("members.details.profile.additionalInfo")}
                        </Text>
                        <Box paddingBlockStart="2">
                          {member.gender && (
                            <Box paddingBlockEnd="2">
                              <Text variant="bodyMd" as="p">
                                <Text fontWeight="semibold" as="span">
                                  {t("members.details.profile.gender")}
                                </Text>
                                {member.gender}
                              </Text>
                            </Box>
                          )}
                          {member.registerDate && (
                            <Box paddingBlockEnd="2">
                              <Text variant="bodyMd" as="p">
                                <Text fontWeight="semibold" as="span">
                                  {t("members.details.profile.registerDate")}
                                </Text>
                                {formatDate(member.registerDate)}
                              </Text>
                            </Box>
                          )}
                        </Box>
                      </Box>
                    )}
                    <Box>
                      <InlineStack align="space-between">
                        <Text variant="bodyMd" fontWeight="bold" as="h3">
                          {t("members.details.profile.birthday")}
                        </Text>
                        <PolarisLink monochrome onClick={handleBirthdayEdit}>
                          {t("members.details.profile.edit")}
                        </PolarisLink>
                      </InlineStack>
                      <Box paddingBlockStart="2">
                        <Text variant="bodyMd" as="p">
                          {formatBirthday(member.birthday)}
                        </Text>
                      </Box>
                    </Box>
                  </BlockStack>
                </Box>
              </BlockStack>
            </Card>
            <Card data-id="social-grid">
              <Box padding="4">
                <BlockStack>
                  <Text variant="bodyMd" fontWeight="semibold" as="h3">
                    {t("members.details.social.title")}
                  </Text>
                  <BlockStack>
                    {["Facebook", "Google", "Line"].map((platform) => (
                      <div key={platform} className="flex justify-between items-center mb-2">
                        <Text variant="bodyMd" as="p">
                          {platform}
                        </Text>
                        <Badge tone="info">{t("members.details.social.notConnected")}</Badge>
                      </div>
                    ))}
                  </BlockStack>
                </BlockStack>
              </Box>
            </Card>
          </BlockStack>
        </Grid.Cell>
      </Grid>
      <Modal
        open={birthdayModalOpen}
        onClose={handleCloseBirthdayModal}
        title={t("members.details.birthdayModal.title")}
        primaryAction={{
          content: t("members.details.birthdayModal.save"),
          onAction: handleSubmitBirthday,
        }}
        secondaryActions={[
          {
            content: t("members.details.birthdayModal.cancel"),
            onAction: handleCloseBirthdayModal,
          },
        ]}
      >
        <Modal.Section>
          <FormLayout>
            <DatePicker
              month={month}
              year={year}
              onChange={handleDateChange}
              onMonthChange={handleMonthChange}
              onYearChange={handleYearChange}
              selected={tempSelectedDate || undefined}
            />
            <TextField
              label={t("members.details.birthdayModal.label")}
              value={tempSelectedDate ? formatDateToYYYYMMDD(tempSelectedDate) : ""}
              readOnly
              helpText={t("members.details.birthdayModal.helpText")}
              autoComplete="off"
            />
          </FormLayout>
        </Modal.Section>
      </Modal>
    </Page>
  );
}
