import { gql } from "@apollo/client/core";
import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node";
import { verifyToken } from "app/utils/auth.server";
import responseBadRequest from "app/utils/response.badRequest";
import Joi from "joi";
import responseSuccess from "../../utils/response.success";
import {
  mapErrors,
  mapJoiErrorToMemberError,
  mapToShopifyInput,
  memberSchema,
  phoneSchema,
  transformCustomerData,
} from "./member.schema";
import { memberQuery, TAIWAN_COUNTRY_PHONE_CODE } from "./query";

export async function loader({ request }: LoaderFunctionArgs) {
  const { client } = await verifyToken(request);

  const url = new URL(request.url);

  const cellphone = url.searchParams.get("cellphone");

  const { error } = Joi.object({
    cellphone: phoneSchema,
  }).validate({ cellphone });

  if (error) {
    return responseBadRequest(mapJoiErrorToMemberError(error.details));
  }

  const result = await client.query({
    query: memberQuery,
    variables: {
      identifier: { phoneNumber: `${TAIWAN_COUNTRY_PHONE_CODE}${cellphone}` },
    },
  });

  if (result.data.customerByIdentifier) {
    return responseSuccess({
      ...transformCustomerData(result.data.customerByIdentifier),
      memberTier: "",
      memberTierStartDate: "",
      memberTierEndDate: "",
      pointRewardType: "",
      pointReward: "",
    });
  }

  return responseBadRequest([
    {
      field: "cellphone",
      code: "invalid_value",
      message: "Member not found. Please check your input and try again.",
    },
  ]);
}

export async function action({ request }: ActionFunctionArgs) {
  const { method } = request;

  if (method !== "POST") {
    return new Response(null, { status: 405 });
  }

  const { client } = await verifyToken(request);

  const body = await request.json().catch((e) => {
    throw responseBadRequest([], e.message);
  });

  try {
    await memberSchema.validateAsync(body, { abortEarly: false });
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  } catch (error: any) {
    return responseBadRequest(mapJoiErrorToMemberError(error.details));
  }

  const bodyData = mapToShopifyInput(body);

  const { data, errors } = await client.mutate({
    mutation: gql`
      mutation ($input: CustomerInput!) {
        customerCreate(input: $input) {
          customer {
            id
            email
            phone
          }
          userErrors {
            field
            message
          }
        }
      }
    `,
    variables: { input: bodyData },
    fetchPolicy: "no-cache",
  });

  if (errors) {
    throw new Response(JSON.stringify(errors), { status: 500 });
  }

  const { userErrors } = data.customerCreate;

  if (userErrors && userErrors.length > 0) {
    return responseBadRequest(mapErrors(userErrors));
  }

  return new Response("", { status: 201 });
}
