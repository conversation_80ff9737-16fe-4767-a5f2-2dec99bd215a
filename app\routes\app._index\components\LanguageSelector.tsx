import { useFetcher } from "@remix-run/react";
import { useAppBridge } from "@shopify/app-bridge-react";
import { Icon, InlineStack, Select } from "@shopify/polaris";
import { GlobeIcon } from "@shopify/polaris-icons";
import { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { langeuageOptions } from "../../../i18n";

export default function LanguageSelector() {
  const shopify = useAppBridge();
  const fetcher = useFetcher();
  const { i18n, t } = useTranslation();
  const [selectedLanguage, setSelectedLanguage] = useState(i18n.language || "en");

  // Effect to handle form submission responses
  useEffect(() => {
    if (fetcher.data && fetcher.formData && fetcher.formData.get("action") === "updateLanguage") {
      shopify.toast.show(t("settings.languageUpdated") || "Language updated successfully");
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fetcher.data, fetcher.formData]);

  const handleLanguageChange = useCallback(
    (value: string) => {
      setSelectedLanguage(value);
      // First change the language in the UI
      i18n.changeLanguage(value).then(() => {
        // Then call action to set the cookie for server-side language detection
        fetcher.submit(
          { action: "updateLanguage", language: value },
          { method: "POST", action: "/app/settings" },
        );
      });
    },
    [i18n, fetcher],
  );

  return (
    <InlineStack align="end">
      <Select
        label={<Icon source={GlobeIcon} tone="base" />}
        options={langeuageOptions}
        labelInline
        onChange={handleLanguageChange}
        value={selectedLanguage}
      />
    </InlineStack>
  );
}
