import { useF<PERSON>cher, use<PERSON>oader<PERSON><PERSON>, useRevalidator } from "@remix-run/react";
import { TitleBar } from "@shopify/app-bridge-react";
import { <PERSON><PERSON>, Card, InlineStack, Layout, Modal, Page, Text } from "@shopify/polaris";
import CardHearderOnAdd from "app/components/CardHearderOnAdd";
import IconText from "app/components/IconText";
import StackList from "app/components/StackList";
import { CheckCircle, CircleDollarSign, Receipt, Trophy } from "lucide-react";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { LoyaltyCard } from "../../components/LoyaltyCard";
import { ICON_MAP } from "../../utils/iconMap";
import { action } from "./action";
import type { WaysEarnReward } from "./interface";
import { loader } from "./loader";

export { action, loader };

/**
 * LoyaltyPage displays loyalties program features in a 2x2 grid with status badges.
 * @returns {JSX.Element} Loyalty program dashboard UI
 */
export default function LoyaltyPage(): JSX.Element {
  const { loyaltyPrograms, shopId, available } = useLoaderData<typeof loader>();
  const { saved } = useLoaderData<{ saved: WaysEarnReward[] }>();
  const { t } = useTranslation();

  const fetcher = useFetcher();
  const revalidator = useRevalidator();
  const [isModalOpen, setModalOpen] = useState(false);
  const [selectedIds, setSelectedIds] = useState<number[]>([]);

  useEffect(() => {
    setSelectedIds([]); // reset selected IDs when modal opens
  }, [isModalOpen]);

  // 🔄 After the fetcher POST action finishes, re-call your loader
  useEffect(() => {
    // fetcher.state goes: "idle" → "submitting" → "loading" → "idle"
    if (fetcher.state === "idle" && fetcher.data) {
      revalidator.revalidate();
    }
  }, [fetcher.state, fetcher.data, revalidator]);

  const toggleSelect = (id: number) => {
    setSelectedIds((prev) => (prev.includes(id) ? prev.filter((x) => x !== id) : [...prev, id]));
  };

  const handleContinue = () => {
    const fd = new FormData();
    fd.append("shopId", String(shopId));
    selectedIds.forEach((id) => fd.append("typeIds", String(id)));

    fetcher.submit(fd, { method: "post", action: "." });

    setModalOpen(false);
  };

  const getProgramStatus = (
    programType: string,
    loyaltyPrograms: Array<{ programType: string; isActive: boolean }>,
  ): "Active" | "Inactive" => {
    const program = loyaltyPrograms.find((p) => p.programType === programType);
    return program?.isActive ? "Active" : "Inactive";
    // Note: The actual translation of "Active"/"Inactive" is handled in the LoyaltyCard component
    // We're just returning the English status here as it's used as a prop value
  };

  return (
    <Page title={t("loyalty.title")}>
      <TitleBar title={t("loyalty.title")} />
      <Layout>
        <Layout.Section>
          <Card>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Points Card */}
              <LoyaltyCard
                icon={<CircleDollarSign className="w-6 h-6" />}
                title={t("loyalties.programTypes.points")}
                status={getProgramStatus("POINTS", loyaltyPrograms)}
                to="/app/loyalties/points"
              />

              {/* Store Credit Card */}
              <LoyaltyCard
                icon={<Receipt className="w-6 h-6" />}
                title={t("loyalties.programTypes.storeCredit")}
                status={getProgramStatus("STORE_CREDIT", loyaltyPrograms)}
                to="/app/loyalties/store-credit"
              />

              {/* VIP Card */}
              <LoyaltyCard
                icon={<Trophy className="w-6 h-6" />}
                title={t("loyalties.programTypes.vip")}
                status={getProgramStatus("VIP_TIER", loyaltyPrograms)}
                to="/app/loyalties/vip"
              />
            </div>
          </Card>
        </Layout.Section>

        {/* --- Ways to earn reward --- */}
        <Layout.Section>
          <Card>
            <CardHearderOnAdd
              headerText={t("loyalty.waysToEarnReward")}
              subHeaderText={t("loyalty.setWaysToEarnRewards")}
              onAdd={() => setModalOpen(true)}
            />
            <StackList initialWays={saved} />
          </Card>
          {/* Modal for Add New */}
          <Modal
            open={isModalOpen}
            onClose={() => setModalOpen(false)}
            title={t("loyalty.selectWaysToEarn")}
          >
            <Modal.Section>
              <Text as="p" tone="subdued">
                {t("loyalty.customizeEarnPoints")}
              </Text>
            </Modal.Section>
            <Modal.Section>
              {available.map((d) => {
                const isSelected = selectedIds.includes(d.id);
                return (
                  <button
                    type="button"
                    key={d.id}
                    className="flex justify-between items-center"
                    onClick={() => toggleSelect(d.id)}
                    style={{
                      display: "flex",
                      alignItems: "center",
                      padding: "12px 24px",
                      marginBottom: 8,
                      border: "1px solid #DFE3E8",
                      borderRadius: 6,
                      backgroundColor: isSelected ? "#FFFBEB" : "#FFF",
                      cursor: "pointer",
                      width: "100%", // make it full-width
                      textAlign: "left", // keep contents left-aligned
                    }}
                    aria-pressed={isSelected} // conveys toggle state
                  >
                    <InlineStack align="start" blockAlign="center" gap="200" wrap={false}>
                      {/* Get icon, title and subtitle Dynamic With mapping code→icon */}
                      <IconText
                        icon={ICON_MAP[d.code].icon}
                        title={ICON_MAP[d.code].label}
                        subtitle={""}
                      />
                    </InlineStack>
                    {/* check mark */}
                    {isSelected && (
                      <CheckCircle
                        color="orange"
                        className="w-5 h-5"
                        style={{ marginLeft: "auto", width: 20, height: 20, strokeWidth: 2.5 }}
                      />
                    )}

                    {/* hidden checkbox input: only selectedIds will be included on submit */}
                    {isSelected && <input type="hidden" name="typeIds" value={d.id} />}
                  </button>
                );
              })}
            </Modal.Section>
            <Modal.Section>
              <InlineStack as="div" align="end" blockAlign="center" gap="200" wrap={false}>
                <Button onClick={() => setModalOpen(false)}>{t("common.cancel")}</Button>
                <Button variant="primary" onClick={handleContinue}>
                  {t("loyalty.continue")}
                </Button>
              </InlineStack>
            </Modal.Section>
          </Modal>
        </Layout.Section>
      </Layout>
    </Page>
  );
}
