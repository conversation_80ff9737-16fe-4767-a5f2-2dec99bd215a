import { useLoaderData } from "@remix-run/react";
import { TabProps, Tabs } from "@shopify/polaris";
import { useCallback, useState } from "react";
import { action } from "./action";
import { loader } from "./loader";
import OrderTab from "./tabs/OrderTab";
import PointsTab from "./tabs/PointsTab";
import { LoyaltyPointsData } from "./types";

// Export the action and loader functions
export { action, loader };

export default function LoyaltyPointPage() {
  const { loyaltyPoints } = useLoaderData<{ loyaltyPoints: LoyaltyPointsData | null }>();
  const [selected, setSelected] = useState(0);

  const handleTabChange = useCallback(
    (selectedTabIndex: number) => setSelected(selectedTabIndex),
    [],
  );

  const tabs: TabProps[] = [
    {
      id: "points-tab",
      content: "Points",
      accessibilityLabel: "Points tab",
      panelID: "points-panel",
    },
    {
      id: "order-tab",
      content: "Order",
      accessibilityLabel: "Order tab",
      panelID: "order-panel",
    },
  ];

  return (
    <>
      <Tabs tabs={tabs} selected={selected} onSelect={handleTabChange} />
      <div className="mt-5">
        {selected === 0 && <PointsTab loyaltyPoints={loyaltyPoints} />}
        {selected === 1 && <OrderTab loyaltyPoints={loyaltyPoints} />}
      </div>
    </>
  );
}
