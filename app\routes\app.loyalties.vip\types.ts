import { LoyaltyProgramType } from "@prisma/client";

/**
 * Response type for action functions
 */
export interface ActionResponse {
  success: boolean;
  message?: string;
  error?: string;
}

/**
 * Type for VIP tier reward
 */
export interface VIPTierReward {
  id: number;
  title: string;
  rewardType: string;
  value: string | null;
}

/**
 * Type for VIP tier
 */
export interface VIPTier {
  id: number;
  name: string;
  loyaltyProgramId: number;
  spendRequirement: number | null;
  pointsRequirement: number | null;
  createdAt: Date;
  updatedAt: Date;
  rewards: VIPTierReward[];
}

/**
 * Type for VIP settings
 */
export interface LoyaltyVIPSettings {
  id: number;
  loyaltyProgramId: number;
  entryMethod: string;
  ordersCount: boolean;
  validationType: string;
  validationDays: number;
  expirationType: string;
  expirationDays: number;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Type for loyalty program with VIP settings
 */
export interface LoyaltyProgramWithVIPSettings {
  id: number;
  shopId: number;
  isActive: boolean;
  programType: LoyaltyProgramType;
  createdAt: Date;
  updatedAt: Date;
  vipSettings: LoyaltyVIPSettings | null;
}

/**
 * Type for loader data
 */
export interface LoaderData {
  vipTiers: VIPTier[];
  loyaltyProgram: LoyaltyProgramWithVIPSettings;
  shop: any;
}
