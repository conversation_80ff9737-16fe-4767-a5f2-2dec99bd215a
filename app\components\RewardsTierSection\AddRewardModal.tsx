import { <PERSON><PERSON>, TitleBar } from "@shopify/app-bridge-react";
import { BlockStack, Button, Icon, InlineStack, Text } from "@shopify/polaris";
import { CashDollarIcon, CreditCardIcon } from "@shopify/polaris-icons";
import { useMemo } from "react";
import { useTranslation } from "react-i18next";
import { REWARD_TYPES, RewardTypeInterface } from "./interface";

interface AddRewardModalProps {
  id: string;
  onSubmit: (value: string) => void;
  onClose: () => void;
  existingRewards?: RewardTypeInterface[];
}

export default function AddRewardModal({
  id,
  onSubmit,
  onClose,
  existingRewards = [],
}: Readonly<AddRewardModalProps>) {
  const { t } = useTranslation();

  // Filter out reward types that are already in the reward list
  const availableRewardOptions = useMemo(() => {
    // Define all available reward options
    const allRewardOptions = [
      {
        id: REWARD_TYPES.POINTS,
        label: t("loyalties.rewards.points"),
        icon: CashDollarIcon,
      },
      {
        id: REWARD_TYPES.STORE_CREDIT,
        label: t("loyalties.rewards.storeCredit"),
        icon: CreditCardIcon,
      },
    ];

    // Get all existing reward types
    const existingRewardTypes = existingRewards.map((reward) => reward.type);

    // Filter out reward options that already exist in the rewards list
    return allRewardOptions.filter((option) => !existingRewardTypes.includes(option.id));
  }, [existingRewards, t]);

  // Show a message if no reward options are available
  if (availableRewardOptions.length === 0) {
    return (
      <Modal id={id}>
        <div style={{ padding: "16px" }}>
          <BlockStack gap="400">
            <Text variant="bodyMd" as="p" alignment="center">
              {t("loyalties.rewards.allRewardsAdded")}
            </Text>
          </BlockStack>
        </div>
        <TitleBar title={t("loyalties.rewards.addNewReward")}>
          <button onClick={() => onClose()}>{t("common.close")}</button>
        </TitleBar>
      </Modal>
    );
  }

  return (
    <Modal id={id}>
      <BlockStack>
        {availableRewardOptions.map((option) => (
          <div
            key={option.id}
            style={{
              padding: "16px 20px",
              borderBottom: "1px solid #e1e3e5",
            }}
          >
            <InlineStack align="space-between" blockAlign="center">
              <InlineStack gap="300" blockAlign="center">
                <div>{<Icon source={option.icon} />}</div>
                <Text as="span" variant="bodyMd">
                  {option.label}
                </Text>
              </InlineStack>
              <Button
                onClick={() => {
                  onSubmit(option.id);
                }}
                size="slim"
              >
                {t("common.add")}
              </Button>
            </InlineStack>
          </div>
        ))}
      </BlockStack>
      <TitleBar title={t("loyalties.rewards.addNewReward")}>
        <button onClick={() => onClose()}>{t("common.cancel")}</button>
      </TitleBar>
    </Modal>
  );
}
