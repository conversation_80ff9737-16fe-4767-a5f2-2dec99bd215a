import type { ActionFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import db from "../db.server";

/**
 * Test route to manually create delayed points records
 * Only for development/testing purposes
 * URL: POST /api/test/delayed-points
 */
export const action = async ({ request }: ActionFunctionArgs) => {
  // Only allow in development
  if (process.env.NODE_ENV === "production") {
    return json({ error: "Not available in production" }, { status: 403 });
  }

  const formData = await request.formData();
  const action = formData.get("action");

  try {
    if (action === "createTestRecord") {
      // Create a test delayed points record that's ready to be processed
      const testRecord = await db.loyaltyPointsHistory.create({
        data: {
          customerId: "gid://shopify/Customer/123456", // Test customer ID
          orderId: "test-order-" + Date.now(),
          points: 100,
          earnedAt: new Date(),
          issueAt: new Date(Date.now() - 1000), // 1 second ago (overdue)
          status: "PENDING",
          createdAt: new Date(),
        },
      });

      return json({
        success: true,
        message: "Test delayed points record created",
        record: testRecord,
      });
    }

    if (action === "processNow") {
      // Dynamically import and run processDelayedPoints
      const { processDelayedPoints } = await import("../utils/tasks.server");
      const result = await processDelayedPoints();

      return json({
        success: true,
        message: "Delayed points processed manually",
        result,
      });
    }

    if (action === "getStats") {
      // Get current statistics
      const stats = await db.loyaltyPointsHistory.groupBy({
        by: ["status"],
        _count: { id: true },
        _sum: { points: true },
      });

      const recentRecords = await db.loyaltyPointsHistory.findMany({
        take: 5,
        orderBy: { createdAt: "desc" },
      });

      return json({
        success: true,
        stats,
        recentRecords,
      });
    }

    return json({ success: false, message: "Invalid action" });
  } catch (error) {
    console.error("Test delayed points error:", error);
    return json(
      {
        success: false,
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
};

// GET method for simple interface
export const loader = async () => {
  if (process.env.NODE_ENV === "production") {
    return json({ error: "Not available in production" }, { status: 403 });
  }

  return json({
    message: "Delayed Points Test Interface",
    endpoints: {
      "POST with action=createTestRecord": "Create a test delayed points record",
      "POST with action=processNow": "Process delayed points immediately",
      "POST with action=getStats": "Get current statistics",
    },
  });
};
