import { ApolloClient, gql } from "@apollo/client/core";
import { ActionFunctionArgs } from "@remix-run/node";
import assert from "assert";
import db from "../../db.server";
import { verifyToken } from "../../utils/auth.server";
import responseBadRequest from "../../utils/response.badRequest";
import responseSuccess from "../../utils/response.success";
import {
  createDraftOrderMutation,
  CreateDraftOrderMutationResponse,
  draftOrderCompleteMutation,
  DraftOrderCompleteMutationResponse,
  refundCreateMutation,
  RefundCreateMutationResponse,
  setMetafieldsMutation,
} from "./mutation";
import {
  IOrder,
  orderMetafields,
  orderSchema,
  transformOrderFromCreateDraftResponse,
  transformOrderToCreateDraftInput,
  transformRefundCreateOutputToResponse,
  transformRelativeOrderToRefundInput,
} from "./order.schema";
import {
  queryMemberFromCellphone,
  queryOrderWithLineItems,
  QueryOrderWithLineItemsResponse,
  queryOrderWithSuggestedRefund,
  QueryOrderWithSuggestedRefundInput,
  QueryOrderWithSuggestedRefundResponse,
} from "./query";

export async function action({ request }: ActionFunctionArgs) {
  if (request.method !== "POST") {
    throw new Response(null, { status: 405 });
  }

  const { client } = await verifyToken(request);
  const body = await request.json().catch((e) => {
    throw responseBadRequest([], e.message);
  });

  // Validate the request body
  const ERROR_MAPPER = {
    "any.required": "missing_field",
    "string.empty": "invalid_value",
    "string.pattern.base": "invalid_value",
    "number.base": "invalid_value",
    "number.positive": "invalid_value",
    "date.format": "invalid_format",
    "any.only": "invalid_value",
    "any.unknown": "invalid_value",
  };
  try {
    await orderSchema.validateAsync(body, { abortEarly: false });
  } catch (error) {
    const validationError = error as {
      details: {
        path: string[];
        type: keyof typeof ERROR_MAPPER;
        message: string;
      }[];
    };

    const validateErrorResponse = validationError.details.map((errorDetail) => ({
      field: errorDetail.path[0],
      code: ERROR_MAPPER[errorDetail.type],
      message: errorDetail.message,
    }));

    // Validate that product SKUs are unique
    const productSkus = new Set<string>();
    for (const product of body.products) {
      if (productSkus.has(product.productSku)) {
        validateErrorResponse.push({
          field: "products",
          code: "invalid_value",
          message: "Product SKUs must be unique.",
        });
      }
      productSkus.add(product.productSku);
    }

    return responseBadRequest(validateErrorResponse);
  }

  if (body.purchaseType === "Sales") {
    return handleSalesOrder(client, body);
  } else {
    return handleReturnOrder(client, body);
  }
}

const handleSalesOrder = async (client: ApolloClient<object>, body: IOrder) => {
  await validateSalesOrder(body);

  // Query `memberId` from `cellphone`
  const memberId = body.cellphone ? 
    (await getMemberFromCellphone(client, `+886${body.cellphone.slice(1)}`)).id
    : undefined;

  // Query product variants from SKU
  const products = await getProductVariantFromSku(client, body);

  // Create the order using GraphQL mutation
  const { data: draftOrderCreateMutationResult } = await client.mutate({
    mutation: createDraftOrderMutation,
    variables: transformOrderToCreateDraftInput(body, memberId, products),
  });

  // Handle mutation errors (if any)
  assert(draftOrderCreateMutationResult as CreateDraftOrderMutationResponse);
  if (draftOrderCreateMutationResult.draftOrderCreate.userErrors.length > 0) {
    console.error(JSON.stringify(draftOrderCreateMutationResult.draftOrderCreate.userErrors));
    throw responseBadRequest(
      draftOrderCreateMutationResult.draftOrderCreate.userErrors.map(
        (error: { field: string[] | null; message: string }) => [
          {
            field: error.field ? error.field.join(".") : "unknown",
            code: "invalid_value",
            message: error.message,
          },
        ],
      ),
    );
  }

  // Validate draft order
  try {
    validateDraftOrder(body, draftOrderCreateMutationResult.draftOrderCreate.draftOrder);
  } catch (error) {
    // Delete draft order
    client.mutate({
      mutation: gql`
        mutation draftOrderDelete($input: DraftOrderDeleteInput!) {
          draftOrderDelete(input: $input) {
            deletedId
            userErrors {
              message
              field
            }
          }
        }
      `,
      variables: {
        input: { id: draftOrderCreateMutationResult.draftOrderCreate.draftOrder.id },
      },
    })

    // Throw the error (again)
    throw error;
  }

  // Complete the draft order
  const shopifyOrderId = await completeDraftOrder(
    client,
    draftOrderCreateMutationResult.draftOrderCreate.draftOrder.id,
  );

  // Set metafields for the newly completed order
  client.mutate({
    mutation: setMetafieldsMutation,
    variables: {
      metafields: orderMetafields(body).map((metafield) => ({
        ...metafield,
        ownerId: shopifyOrderId,
      })),
    },
  });

  // Mark order as paid
  client.mutate({
    mutation: gql`
      mutation orderMarkAsPaid($input: OrderMarkAsPaidInput!) {
        orderMarkAsPaid(input: $input) {
          userErrors {
            field
            message
          }
        }
      }
    `,
    variables: {
      input: { id: shopifyOrderId },
    },
  });

  // Process the order and generate response
  return responseSuccess({
    memberId: memberId,
    ...transformOrderFromCreateDraftResponse(draftOrderCreateMutationResult),
  });
};

const handleReturnOrder = async (client: ApolloClient<object>, body: IOrder) => {
  // Query `memberId` from `cellphone`
  const memberId = body.cellphone ?
    (await getMemberFromCellphone(client, `+886${body.cellphone.slice(1)}`)).id
    : undefined;

  // Find the order from the database
  const salesOrderPrisma = await findOrderFromDatabase(body);

  // Find the line items from the database
  const salesOrderLineItems = await findLineItemsFromDatabase(client, salesOrderPrisma.ordShopiId);

  // Map line items to query order input
  const lineItemsAsInput = mapLineItemsToQueryOrderInput(body, salesOrderLineItems);

  // Prepare query order input
  const queryOrderInput = {
    id: salesOrderPrisma.ordShopiId,
    refundLineItems: lineItemsAsInput,
  };
  assert(queryOrderInput as QueryOrderWithSuggestedRefundInput);

  // Query order with suggested refund
  const relativeOrder = await findOrderWithSuggestedRefund(client, queryOrderInput);

  // Validate suggested refund
  validateSuggestedRefund(body, relativeOrder.order);

  // Validate that the member is the owner of the order
  if (memberId !== relativeOrder.order.customer?.id) {
    throw responseBadRequest([
      {
        field: "cellphone",
        code: "invalid_value",
        message: "Member with the specified 'cellphone' is not the owner of the order.",
      },
    ]);
  }

  // Create refund order
  const { data: refundCreateMutationResult } = await client.mutate({
    mutation: refundCreateMutation,
    variables: transformRelativeOrderToRefundInput(relativeOrder.order),
  });
  assert(refundCreateMutationResult as RefundCreateMutationResponse);

  // Update return order to database
  const returnOrderPrisma = await updateReturnOrderToDatabase(
    body.orderId,
    refundCreateMutationResult.refundCreate.refund.id,
    salesOrderPrisma.id,
  );

  // Return the refund order
  return responseSuccess(
    transformRefundCreateOutputToResponse(
      refundCreateMutationResult.refundCreate,
      returnOrderPrisma.orderId,
    ),
  );
};

const validateSalesOrder = async (body: IOrder) => {
  // Verify that `orderId` not yet exists in the database
  const existingSalesOrder = await db.salesOrders.findFirst({
    where: {
      orderPosId: body.orderId,
    },
  });
  if (existingSalesOrder) {
    throw responseBadRequest([
      {
        field: "orderId",
        code: "duplicate_value",
        message: `OrderId '${body.orderId}' has already been taken`,
      },
    ]);
  }
};

const getMemberFromCellphone = async (
  client: ApolloClient<object>,
  phoneNumber: string,
): Promise<{ id: string; email: string }> => {
  const {
    data: {
      customerByIdentifier,
    },
  } = await client
    .query({
      query: queryMemberFromCellphone,
      variables: {
        identifier: { phoneNumber: phoneNumber },
      },
    })
    // .catch(() => {
    //   throw responseBadRequest([
    //     {
    //       field: "cellphone",
    //       code: "not_found",
    //       message: "Member with the specified 'cellphone' is not found.",
    //     },
    //   ]);
    // });

  if (!customerByIdentifier) {
    throw responseBadRequest([
      {
        field: "cellphone",
        code: "not_found",
        message: "Member with the specified 'cellphone' is not found.",
      },
    ]);
  }
  return customerByIdentifier;
};

const getProductVariantFromSku = async (client: ApolloClient<object>, body: IOrder) => {
  const productVariantsQueries = body.products.map(
    (productVariant, index) => `
      productVariants${index}: productVariants(query: "${productVariant.productSku}", first: 1) {
        nodes {
          id
          sku
          title
          price
          product {
            id
            title
          }
        }
      }
    `,
  );

  const { data: productVariantsQueryResults } = await client.query({
    query: gql`
      query {
        ${productVariantsQueries.join("\n")}
      }
    `,
  });

  return Object.values(productVariantsQueryResults).map((productVariant, index) => {
    const variant = productVariant as {
      nodes: {
        id: string;
        sku: string;
        title: string;
        price: number;
        product: { id: string; title: string };
      }[];
    };
    if (variant.nodes.length === 0) {
      // Handle exception before a dummy item fails to be created
      const { unitPrice, productName } = body.products[index];
      if (!unitPrice || !productName) {
        const errors = [];
        if (!unitPrice) {
          errors.push({
            field: "unitPrice",
            code: "missing_field",
            message:
              "'unitPrice' must be provided if product with the specified SKU is not yet declared in Shopify.",
          });
        }
        if (!productName) {
          errors.push({
            field: "productName",
            code: "missing_field",
            message:
              "'productName' must be provided if product with the specified SKU is not yet declared in Shopify.",
          });
        }
        throw responseBadRequest(errors);
      }
      return body.products[index];
    } else {
      const {
        id: variantId,
        price: unitPrice,
        product: { id: productId, title: productName },
      } = variant.nodes[0];

      // Construct the product variant object to return
      return {
        variantId,
        productId,
        productName,
        unitPrice,
        ...body.products[index],
      };
    }
  });
};

const completeDraftOrder = async (client: ApolloClient<object>, draftOrderId: string) => {
  const { data: draftOrderCompleteMutationResult } = await client.mutate({
    mutation: draftOrderCompleteMutation,
    variables: {
      id: draftOrderId,
    },
  });
  assert(draftOrderCompleteMutationResult as DraftOrderCompleteMutationResponse);
  // BUG: Metafields are not being set. Should get metafield with key "order_id" from the draft order instead
  storeSalesOrderToDatabase(draftOrderCompleteMutationResult.draftOrderComplete.draftOrder);
  return draftOrderCompleteMutationResult.draftOrderComplete.draftOrder.order.id;
};

const storeSalesOrderToDatabase = async (
  draftOrder: DraftOrderCompleteMutationResponse["draftOrderComplete"]["draftOrder"],
) => {
  // Create sales order
  await db.salesOrders.create({
    data: {
      orderPosId: draftOrder.metafield.value,
      ordShopiId: draftOrder.order.id,
    },
  });
};

const validateDraftOrder = (
  body: IOrder,
  draftOrder: CreateDraftOrderMutationResponse["draftOrderCreate"]["draftOrder"]
) => {
  const validationErrors = [];
  if (Number(draftOrder.totalLineItemsPriceSet.shopMoney.amount) !== body.totalOrderAmount) {
    validationErrors.push({
      field: "totalOrderAmount",
      code: "calculation_mismatch",
      message: `Total order amount does not match the calculated value`,
      detail: {
        expected: Number(draftOrder.totalLineItemsPriceSet.shopMoney.amount),
        actual: body.totalOrderAmount,
      }
    });
  }
  if (Number(draftOrder.totalDiscountsSet.shopMoney.amount) !== body.discountAmount) {
    validationErrors.push({
      field: "discountAmount",
      code: "calculation_mismatch",
      message: `Discount amount does not match the calculated value`,
      detail: {
        expected: Number(draftOrder.totalDiscountsSet.shopMoney.amount),
        actual: body.discountAmount,
      }
    });
  }
  if (Number(draftOrder.totalPriceSet.shopMoney.amount) !== body.actualOrderAmount) {
    validationErrors.push({
      field: "actualOrderAmount",
      code: "calculation_mismatch",
      message: `Actual order amount does not match the calculated value`,
      detail: {
        expected: Number(draftOrder.totalPriceSet.shopMoney.amount),
        actual: body.actualOrderAmount,
      }
    });
  }
  if (body.discountCodes && draftOrder.discountCodes.length !== body.discountCodes.length) {
    const invalidDiscountCodes = []
    for (const discountCode of body.discountCodes) {
      if (!draftOrder.discountCodes.includes(discountCode)) {
        invalidDiscountCodes.push(discountCode);
      }
    }
    validationErrors.push({
      field: "discountCodes",
      code: "invalid_discount_codes",
      message: `Some or all discount codes are not applicable`,
      detail: {
        valid_codes: draftOrder.discountCodes,
        invalid_codes: invalidDiscountCodes,
        // actual: body.discountCodes,
      }
    });
  }

  if (validationErrors.length > 0) {
    throw responseBadRequest(validationErrors);
  }
}

const findOrderFromDatabase = async (body: IOrder) =>
  await db.salesOrders
    .findFirstOrThrow({
      where: {
        orderPosId: body.relativeOrderId,
      },
    })
    .catch(() => {
      throw responseBadRequest([
        {
          field: "orderId",
          code: "not_found",
          message: `Sales order with ID '${body.relativeOrderId}' is not found`,
        },
      ]);
    });

const findLineItemsFromDatabase = async (
  client: ApolloClient<object>,
  salesOrdShopId: string,
): Promise<QueryOrderWithLineItemsResponse["order"]["lineItems"]["nodes"]> =>
  (
    await client.query({
      query: queryOrderWithLineItems,
      variables: { id: salesOrdShopId },
    })
  ).data.order.lineItems.nodes;

const mapLineItemsToQueryOrderInput = (
  body: IOrder,
  salesOrderLineItemNodes: QueryOrderWithLineItemsResponse["order"]["lineItems"]["nodes"],
) =>
  body.products.map((product) => {
    const lineItem = salesOrderLineItemNodes.find(
      (lineItem) => lineItem.sku === product.productSku,
    );
    if (!lineItem) {
      throw responseBadRequest([
        {
          field: "products",
          code: "not_found",
          message: `Product with SKU '${product.productSku}' is not found in the order.`,
        },
      ]);
    }
    return {
      lineItemId: lineItem.id,
      quantity: product.qty,
    };
  });

const findOrderWithSuggestedRefund = async (
  client: ApolloClient<object>,
  queryOrderInput: QueryOrderWithSuggestedRefundInput,
): Promise<QueryOrderWithSuggestedRefundResponse> =>
  (
    await client
      .query({
        query: queryOrderWithSuggestedRefund,
        variables: queryOrderInput,
      })
      .catch((error) => {
        if (error.graphQLErrors?.[0]?.path?.includes("order", "suggestedRefund")) {
          throw responseBadRequest([
            {
              field: "products",
              code: "exceeds_purchase_quantity",
              message: "Cannot refund more items than were purchased.",
            },
          ]);
        } else {
          throw error;
        }
      })
  ).data;

const validateSuggestedRefund = (
  body: IOrder,
  relativeOrder: QueryOrderWithSuggestedRefundResponse["order"],
) => {
  const validationErrors = [];
  if (-Number(relativeOrder.suggestedRefund.amountSet.shopMoney.amount) !== body.actualOrderAmount) {
    validationErrors.push({
      field: "actualOrderAmount",
      code: "calculation_mismatch",
      message: `Total refunded amount does not match the calculated value`,
      detail: {
        expected: -Number(relativeOrder.suggestedRefund.amountSet.shopMoney.amount),
        acutal: body.totalOrderAmount,
      }
    });
  }

  if (validationErrors.length > 0) {
    throw responseBadRequest(validationErrors);
  }
}

const updateReturnOrderToDatabase = async (
  refundOrderId: string,
  refundOrderShopId: string,
  salesOrderLocalId: number,
) =>
  await db.returnOrders.create({
    data: {
      orderId: refundOrderId,
      retOrdShopId: refundOrderShopId,
      salesOrdId: salesOrderLocalId,
    },
  });
