import { LoaderFunctionArgs } from "@remix-run/node";
import dayjs from "dayjs";
import <PERSON><PERSON> from "joi";
import { verifyToken } from "../../utils/auth.server";
import responseBadRequest from "../../utils/response.badRequest";
import responseSuccess from "../../utils/response.success";
import { phoneSchema } from "../api.members/member.schema";
import { memberQuery, TAIWAN_COUNTRY_PHONE_CODE } from "../api.members/query";
import {
  collectionsQuery,
  customerSegmentMembershipQuery,
  offersQuery,
  productsQuery,
  segmentsQuery,
} from "./query";
import {
  DiscountInterface,
  getDiscountPercentage,
  getDiscountPrice,
  getDiscountType,
  getGiftDiscountAmount,
  getGiftDiscountPercentage,
  getGiftQty,
  getGifts,
  getIsGift,
  getMinPurchaseAmount,
  getProductQty,
  getProducts,
  getUsingMinPrice,
  getUsing<PERSON>in<PERSON><PERSON>,
  parseIfNumber,
} from "./util";

export async function loader({ request }: LoaderFunctionArgs) {
  const { client } = await verifyToken(request);

  const url = new URL(request.url);

  const cellphone = url.searchParams.get("cellphone");

  const { error } = Joi.object({
    cellphone: phoneSchema,
  }).validate({ cellphone });

  if (error) {
    return responseBadRequest(error.details);
  }

  // find customer
  const { data: memberData, errors: memberErrors } = await client.query({
    query: memberQuery,
    variables: {
      identifier: { phoneNumber: `${TAIWAN_COUNTRY_PHONE_CODE}${cellphone}` },
    },
  });

  if (memberErrors) {
    throw new Response(JSON.stringify(memberErrors), { status: 500 });
  }

  const member = memberData.customerByIdentifier;

  if (!memberData.customerByIdentifier) {
    return responseBadRequest([
      {
        field: "cellphone",
        code: "invalid_value",
        message: "Member not found. Please check your input and try again.",
      },
    ]);
  }

  // find all segments
  const { data: allSegmentsData, errors: allSegmentsErrors } = await client.query({
    query: segmentsQuery,
  });

  if (allSegmentsErrors) {
    throw new Response(JSON.stringify(allSegmentsErrors), { status: 500 });
  }
  // check which segments that customer is in

  const { data: customerSegmentMembershipData } = await client.query({
    query: customerSegmentMembershipQuery,
    variables: {
      customerId: member.id,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      segmentIds: allSegmentsData.segments.nodes.map((node: any) => node.id),
    },
  });

  const segmentIds = customerSegmentMembershipData.customerSegmentMembership.memberships
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    .filter((membership: any) => membership.isMember)
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    .map((membership: any) => membership.segmentId);

  const { data, errors } = await client.query({
    query: offersQuery,
  });

  if (errors) {
    throw new Response(JSON.stringify(errors), { status: 500 });
  }

  const filteredDiscounts: DiscountInterface[] = data.discountNodes.edges
    .filter((edge: { node: { discount: DiscountInterface } }) => {
      const discount = edge.node.discount;
      if (discount.customerSelection?.segments) {
        return discount.customerSelection.segments.some((segment) =>
          segmentIds.includes(segment.id),
        );
      }
      if (discount.customerSelection?.customers) {
        return discount.customerSelection.customers.some(
          (customerId) => customerId?.id === member.id,
        );
      }
      return true;
    })
    .map((edge: { node: { discount: DiscountInterface; id: string } }) => ({
      ...edge.node.discount,
      id: edge.node.id,
    }));

  const collectionIds = new Set<string>();
  for (const discount of filteredDiscounts) {
    if (discount.customerBuys?.items?.collections?.edges) {
      for (const edge of discount.customerBuys.items.collections.edges) {
        collectionIds.add(edge.node.id);
      }
    }
    if (discount.customerGets?.items?.collections?.nodes) {
      for (const node of discount.customerGets.items.collections.nodes) {
        collectionIds.add(node.id);
      }
    }
  }

  const { data: collectionData } = await client.query({
    query: collectionsQuery,
    variables: {
      first: Array.from(collectionIds).length,
      ids: Array.from(collectionIds)
        .map((id) => {
          const idString = id.split("/");
          return `id:${idString[idString.length - 1]}`;
        })
        .join(" OR "),
    },
  });

  const productIds = new Set<string>();
  for (const discount of filteredDiscounts) {
    if (discount.customerBuys?.items?.products?.edges) {
      for (const edge of discount.customerBuys.items.products.edges) {
        productIds.add(edge.node.id);
      }
    }
    if (discount.customerGets?.items?.products?.nodes) {
      for (const edge of discount?.customerGets?.items?.products?.nodes ?? []) {
        productIds.add(edge.id);
      }
    }
  }

  const { data: productData } = await client.query({
    query: productsQuery,
    variables: {
      first: Array.from(productIds).length,
      ids: Array.from(productIds)
        .map((id) => {
          const idString = id.split("/");
          return `id:${idString[idString.length - 1]}`;
        })
        .join(" OR "),
    },
  });

  const result = filteredDiscounts.map((discount: DiscountInterface) => {
    return {
      discountId: discount.id,
      discountName: discount.title,
      discountCode: discount.codes?.nodes.map((node) => node.code)[0] ?? null,
      typeAutomaticOrCodeDiscount: discount.__typename?.startsWith("DiscountAutomatic")
        ? "Automatic"
        : "Code",
      discountExpireDate: discount.endsAt ? dayjs(discount.endsAt).format("YYYY-MM-DD") : null,
      discountType: discount.__typename ? getDiscountType(discount) : null,
      discountPrice: getDiscountPrice(discount),
      discountPercentage: getDiscountPercentage(discount),
      isGift: getIsGift(discount),
      usingMinQty: parseIfNumber(getUsingMinQty(discount) as unknown as string),
      usingMinPrice: getUsingMinPrice(discount),
      countOfDiscount: discount?.asyncUsageCount ?? null,
      products: getProducts(discount, collectionData.collections.nodes, productData.products.nodes),
      productQty: parseIfNumber(getProductQty(discount) as unknown as string),
      minPurchaseAmount: getMinPurchaseAmount(discount),
      gifts: getGifts(discount, collectionData.collections.nodes, productData.products.nodes),
      giftQty: parseIfNumber(getGiftQty(discount) as unknown as string),
      giftDiscountPercentage: getGiftDiscountPercentage(discount),
      giftDiscountAmount: getGiftDiscountAmount(discount),
    };
  });

  return responseSuccess(result);
}
