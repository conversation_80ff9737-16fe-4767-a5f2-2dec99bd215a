import { DiscountType, LoyaltyProgramType, RequirementType, RewardType } from "@prisma/client";
import { ActionFunctionArgs } from "@remix-run/node";
import { REWARD_TYPES, RewardTypeInterface } from "../../components/RewardsSection/interface";
import db from "../../db.server";
import { authenticate } from "../../shopify.server";

/**
 * Action function for creating a new VIP tier
 */
export async function action({ request }: ActionFunctionArgs) {
  try {
    const { session } = await authenticate.admin(request);

    // Parse JSON data from request
    const requestJson = await request.json();
    const { tierName, spendRequirement: spendRequirementStr, rewards } = requestJson;

    // Convert spendRequirement to a number
    const spendRequirement = parseFloat(spendRequirementStr);

    if (!tierName) {
      return Response.json({ success: false, error: "Tier name is required" }, { status: 400 });
    }

    if (isNaN(spendRequirement)) {
      return Response.json(
        { success: false, error: "Spend requirement must be a valid number" },
        { status: 400 },
      );
    }

    // Get the shop information
    const shop = await db.shop.findFirst({
      where: {
        myshopifyDomain: session.shop,
      },
    });

    if (!shop) {
      return Response.json(
        { success: false, error: `Shop not found for domain ${session.shop}` },
        { status: 400 },
      );
    }

    // Find or create loyalty program for VIP tiers
    let loyaltyProgram = await db.loyaltyProgram.findFirst({
      where: {
        shopId: shop.id,
        programType: LoyaltyProgramType.VIP_TIER,
      },
    });

    // Create a new loyalty program for VIP tiers
    loyaltyProgram ??= await db.loyaltyProgram.create({
      data: {
        shopId: shop.id,
        programType: LoyaltyProgramType.VIP_TIER,
        isActive: true,
      },
    });

    // Check if tier name already exists for this loyalty program
    const existingTier = await db.loyaltyVIPTier.findFirst({
      where: {
        loyaltyProgramId: loyaltyProgram.id,
        name: tierName,
      },
    });

    if (existingTier) {
      return Response.json(
        { success: false, error: "A tier with this name already exists" },
        { status: 400 },
      );
    }

    // Create a new VIP tier with rewards
    const newVipTier = await db.loyaltyVIPTier.create({
      data: {
        loyaltyProgramId: loyaltyProgram.id,
        name: tierName,
        spendRequirement: spendRequirement,
        // Create rewards in the new table
        rewards: {
          create: rewards.map((reward: RewardTypeInterface) => {
            // Map the reward type to the enum
            const mapRewardType = (type: string): RewardType => {
              switch (type) {
                case REWARD_TYPES.POINTS:
                  return RewardType.POINTS;
                case REWARD_TYPES.STORE_CREDIT:
                  return RewardType.STORE_CREDIT;
                case REWARD_TYPES.AMOUNT_OFF:
                  return RewardType.AMOUNT_OFF;
                case REWARD_TYPES.FREE_SHIPPING:
                  return RewardType.FREE_SHIPPING;
                default:
                  return RewardType.POINTS;
              }
            };

            // Map the discount type to the enum
            const mapDiscountType = (type: string): DiscountType => {
              return type === "percentage" ? DiscountType.PERCENTAGE : DiscountType.FIXED;
            };

            // Map the requirement type to the enum
            const mapRequirementType = (type: string): RequirementType => {
              switch (type) {
                case "none":
                  return RequirementType.NONE;
                case "amount":
                  return RequirementType.AMOUNT;
                case "quantity":
                  return RequirementType.QUANTITY;
                default:
                  return RequirementType.NONE;
              }
            };

            const baseReward = {
              title: reward.title,
              rewardType: mapRewardType(reward.type),
              value: "value" in reward ? reward.value : null,
            };

            // Add specific fields based on reward type
            if (reward.type === REWARD_TYPES.AMOUNT_OFF) {
              return {
                ...baseReward,
                discountType: mapDiscountType(reward.discountType),
                minimumRequirement: mapRequirementType(reward.minimumRequirement),
                minimumValue: reward.minimumValue ? parseFloat(reward.minimumValue) : null,
                productDiscounts: reward.combinations.productDiscounts,
                orderDiscounts: reward.combinations.orderDiscounts,
                shippingDiscounts: reward.combinations.shippingDiscounts,
              };
            } else if (reward.type === REWARD_TYPES.FREE_SHIPPING) {
              return {
                ...baseReward,
                minimumRequirement: mapRequirementType(reward.minimumRequirement),
                minimumValue: reward.minimumValue ? parseFloat(reward.minimumValue) : null,
                productDiscounts: reward.combinations.productDiscounts,
                orderDiscounts: reward.combinations.orderDiscounts,
              };
            }

            return baseReward;
          }),
        },
      },
      include: {
        rewards: true, // Include rewards in the response
      },
    });

    return Response.json({ success: true, vipTier: newVipTier });
  } catch (error) {
    console.error("Error creating VIP tier:", error);
    return Response.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "An unexpected error occurred",
      },
      { status: 500 },
    );
  }
}
