import { useFetcher } from "@remix-run/react";
import { useAppBridge } from "@shopify/app-bridge-react";
import {
  BlockStack,
  Box,
  Button,
  Card,
  ChoiceList,
  InlineGrid,
  InlineStack,
  Text,
  TextField,
} from "@shopify/polaris";
import { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

interface ValidationMethodDisplayProps {
  /**
   * Initial validation type from the server
   */
  initialValidationType: string;

  /**
   * Initial validation days from the server
   */
  initialValidationDays: number;

  /**
   * Default validation type constant
   */
  defaultValidationType: string;

  /**
   * Action type for form submission
   */
  actionType: string;
}

/**
 * A component that displays and allows editing of the tier validation method
 */
export default function ValidationMethodDisplay({
  initialValidationType,
  initialValidationDays,
  defaultValidationType,
  actionType,
}: Readonly<ValidationMethodDisplayProps>) {
  // Get App Bridge for toast notifications
  const shopify = useAppBridge();

  // Fetcher for form submissions
  const fetcher = useFetcher();

  // Get translation function
  const { t } = useTranslation();

  // Main state for validation settings
  const [validationType, setValidationType] = useState(initialValidationType);
  const [validationDays, setValidationDays] = useState(initialValidationDays.toString());

  // Convert validation type to array format for ChoiceList
  const [selectedValidation, setSelectedValidation] = useState<string[]>([
    validationType || defaultValidationType,
  ]);

  // State for editing validation
  const [isEditingValidation, setIsEditingValidation] = useState(false);
  const [tempValidation, setTempValidation] = useState<string[]>(selectedValidation);
  const [tempValidationDays, setTempValidationDays] = useState(validationDays);

  // Update internal state when props change
  useEffect(() => {
    setValidationType(initialValidationType);
    setValidationDays(initialValidationDays.toString());
    setSelectedValidation([initialValidationType || defaultValidationType]);
  }, [initialValidationType, initialValidationDays, defaultValidationType]);

  // Effect to handle form submission responses
  useEffect(() => {
    if (fetcher.data && fetcher.state === "idle") {
      const response = fetcher.data as { success: boolean; message?: string; error?: string };
      if (response.success) {
        // Show success toast using App Bridge Toast API
        shopify?.toast.show(
          response.message ?? t("loyalties.vip.toastMessages.validationUpdated"),
          {
            isError: false,
            duration: 4000,
          },
        );
      } else if (response.error) {
        // Show error toast using App Bridge Toast API
        shopify?.toast.show(response.error, {
          isError: true,
          duration: 4000,
        });
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fetcher.data, fetcher.state]);

  const handleEditValidation = useCallback(() => {
    // Initialize temp values with current values
    setTempValidation(selectedValidation);
    setTempValidationDays(validationDays);
    setIsEditingValidation(true);
  }, [selectedValidation, validationDays]);

  const handleCancelValidationEdit = useCallback(() => {
    setIsEditingValidation(false);
    // Reset temp values to current values
    setTempValidation(selectedValidation);
    setTempValidationDays(validationDays);
  }, [selectedValidation, validationDays]);

  const handleSaveValidation = useCallback(() => {
    setIsEditingValidation(false);

    // Calculate validation days
    const days = !tempValidation.includes(defaultValidationType)
      ? parseInt(tempValidationDays ?? "0", 10)
      : 0;

    // Update local state
    setSelectedValidation(tempValidation);
    setValidationDays(days.toString());
    setValidationType(tempValidation[0] ?? defaultValidationType);

    // Submit to server
    const formData = {
      actionType: actionType,
      validationType: tempValidation[0] ?? defaultValidationType,
      validationDays: days,
    };

    fetcher.submit(formData, { method: "post", encType: "application/json" });
  }, [tempValidation, tempValidationDays, defaultValidationType, actionType, fetcher]);

  return (
    <BlockStack gap="200">
      <Card roundedAbove="xs">
        <InlineGrid columns={2}>
          <BlockStack gap="200">
            <Text variant="headingMd" as="h2">
              {t("loyalties.vip.validation.title")}
            </Text>
            <Text as="p" variant="bodyMd" tone="subdued">
              {t("loyalties.vip.validation.description")}
            </Text>
          </BlockStack>

          <BlockStack gap="200" align="center">
            {isEditingValidation ? (
              <>
                <ChoiceList
                  title={undefined}
                  choices={[
                    {
                      label: t("loyalties.vip.validation.immediately"),
                      value: defaultValidationType,
                    },
                    {
                      label: t("loyalties.vip.validation.daysAfterPaid"),
                      value: "days_after_paid",
                    },
                  ]}
                  selected={tempValidation}
                  onChange={setTempValidation}
                />

                {!tempValidation.includes(defaultValidationType) && (
                  <TextField
                    label=""
                    labelHidden
                    type="number"
                    value={tempValidationDays}
                    onChange={setTempValidationDays}
                    autoComplete="off"
                    suffix={t("loyalties.vip.validation.daysAfterPaid").split(" ")[0]}
                  />
                )}

                <Box paddingBlockStart="300">
                  <InlineStack align="end" gap="200">
                    <Button variant="tertiary" onClick={handleCancelValidationEdit}>
                      {t("loyalties.vip.validation.cancel")}
                    </Button>
                    <Button variant="primary" onClick={handleSaveValidation}>
                      {t("loyalties.vip.validation.save")}
                    </Button>
                  </InlineStack>
                </Box>
              </>
            ) : (
              <InlineStack blockAlign="end" align="space-between">
                <Text as="p" variant="bodyMd">
                  {selectedValidation.includes(defaultValidationType)
                    ? t("loyalties.vip.validation.immediately")
                    : `${validationDays} ${t("loyalties.vip.validation.daysAfterPaid")}`}
                </Text>

                <Button variant="tertiary" onClick={handleEditValidation}>
                  {t("loyalties.vip.entryMethod.edit")}
                </Button>
              </InlineStack>
            )}
          </BlockStack>
        </InlineGrid>
      </Card>
    </BlockStack>
  );
}
