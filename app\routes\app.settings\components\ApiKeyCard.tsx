import { useLoaderData } from "@remix-run/react";
import { useAppBridge } from "@shopify/app-bridge-react";
import { <PERSON><PERSON>, <PERSON> } from "@shopify/polaris";
import { ClipboardIcon } from "@shopify/polaris-icons";
import { Eye, EyeOff, RefreshCw } from "lucide-react";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { FetcherType, LoaderData } from "../types";

export default function ApiKeyCard({ fetcher }: { readonly fetcher: FetcherType }) {
  const [apiKey, setApiKey] = useState<string | null>(null);
  const [showApiKey, setShowApiKey] = useState(false);
  const { t } = useTranslation();
  const shopify = useAppBridge();
  const loaderData = useLoaderData<LoaderData>();

  useEffect(() => {
    if (loaderData.apiKey) {
      setApiKey(loaderData.apiKey);
    }
  }, [loaderData]);

  useEffect(() => {
    if (fetcher.data && Object.hasOwn(fetcher.data, "apiKey")) {
      shopify.toast.show(t("settings.keyUpdated"));
      setApiKey(fetcher.data.apiKey);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fetcher.data]);

  const toggleShowApiKey = () => {
    setShowApiKey(!showApiKey);
  };

  const copyToClipboard = () => {
    navigator.clipboard.writeText(apiKey ?? "");
    shopify.toast.show(t("settings.keyCopied"));
  };

  const generateNewApiKey = () => {
    fetcher.submit({ action: "generateApiKey" }, { method: "POST" });
  };

  return (
    <Card>
      <div className="p-2">
        <div className="space-y-4">
          <div className="space-y-2">
            <label
              htmlFor="api-key"
              className="block text-sm font-medium text-gray-700 dark:text-gray-300"
            >
              {t("settings.apiKey")}
            </label>
            <div className="flex">
              <div className="relative flex-grow mr-3">
                <input
                  id="api-key"
                  type={showApiKey ? "text" : "password"}
                  value={apiKey ?? ""}
                  onChange={(e) => setApiKey(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white pr-10"
                  readOnly
                />
                <button
                  className="absolute right-0 top-0 h-full px-3 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                  onClick={toggleShowApiKey}
                  type="button"
                  aria-label={showApiKey ? "Hide API Key" : "Show API Key"}
                >
                  {showApiKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </button>
              </div>
              <Button size="slim" onClick={copyToClipboard} icon={ClipboardIcon}>
                {t("settings.copyButton") || "Copy"}
              </Button>
            </div>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {t("settings.securityWarning")}
            </p>
          </div>
        </div>
      </div>

      <div className="p-2 border-t border-gray-200 dark:border-gray-700 flex justify-end">
        <button
          onClick={generateNewApiKey}
          disabled={fetcher.state === "submitting" || fetcher.state === "loading"}
          className={`flex items-center px-4 py-2 rounded-md text-white ${
            fetcher.state === "submitting" || fetcher.state === "loading"
              ? "bg-blue-400 cursor-not-allowed"
              : "bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          }`}
        >
          {fetcher.state === "submitting" || fetcher.state === "loading" ? (
            <>
              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
              {t("settings.generating")}
            </>
          ) : (
            <>
              <RefreshCw className="mr-2 h-4 w-4" />
              {t("settings.generateNewKey")}
            </>
          )}
        </button>
      </div>
    </Card>
  );
}
