query CartInput {
  cart {
    usingPoints: attribute(key: "usingPoints") {
      key
      value
    }
    discountValue: attribute(key: "discountValue") {
      key
      value
    }

    buyerIdentity {
      customer {
        metafield(key: "points", namespace: "$app") {
          value
          type
        }
      }
    }
    cost {
      totalAmount {
        currencyCode
      }
    }
  }
  discount {
    discountClasses
  }
}
