import { LoaderFunctionArgs } from "@remix-run/node";
import { useCallback, useState } from "react";
import {
  Page,
  Card,
  TextField,
  Text,
  List,
  Layout,
  Checkbox,
  Button,
  InlineStack,
  InlineGrid,
  BlockStack,
} from "@shopify/polaris";
import { useF<PERSON>cher, useLoaderData, useNavigate, useSearchParams } from '@remix-run/react';

import db from "../../db.server";
import responseBadRequest from '../../utils/response.badRequest';

export const loader = async ({ params }: LoaderFunctionArgs) => {
  const { id: rewardId } = params;

  if (!rewardId) {
    throw responseBadRequest("Reward ID is required");
  }
  
  else if (rewardId === "new") {
    return {
      id: null,
      birthdayRewardType: "STORE_CREDIT",
      title: "",
      expiryMonths: null,
      rewardValue: 0,
    };
  }
  
  else if (isNaN(Number(rewardId))) {
    throw responseBadRequest("Reward ID is not a number");
  }

  else {
    // Get reward details from local database
    const reward = await db.birthdayReward.findFirstOrThrow({
      where: {
        id: Number(rewardId),
      },
      select: {
        id: true,
        birthdayRewardType: true,
        title: true,
        expiryMonths: true,
        rewardValue: true,
      },
    });
    return reward;
  }
}

export const action = async ({ request }: LoaderFunctionArgs) => {
  if (request.method === "POST") {
    const { settingsId, title, rewardValue, expiryMonths } = await request.json();
    console.log("Submitted data:", { settingsId, title, rewardValue, expiryMonths });

    // Create the birthday reward
    const birthdayReward = await db.birthdayReward.create({
      data: {
        settingsId: Number(settingsId),
        birthdayRewardType: "STORE_CREDIT",
        title: title,
        expiryMonths: expiryMonths ? Number(expiryMonths) : undefined,
        rewardValue: Number(rewardValue),
      },
      select: {
        id: true,
        birthdayRewardType: true,
        title: true,
        expiryMonths: true,
        rewardValue: true,
      },
    });
    return birthdayReward;
  }

  else if (request.method === "PATCH") {
    const { id, title, rewardValue, expiryMonths } = await request.json();

    // Update the birthday reward
    const birthdayReward = await db.birthdayReward.update({
      where: {
        id: Number(id),
      },
      data: {
        title: title,
        expiryMonths: expiryMonths ? Number(expiryMonths) : undefined,
        rewardValue: Number(rewardValue),
      },
      select: {
        id: true,
        birthdayRewardType: true,
        title: true,
        expiryMonths: true,
        rewardValue: true,
      },
    });
    return birthdayReward;
  }

  else if (request.method === "DELETE") {
    const { id } = await request.json();

    // Delete the birthday reward
    const birthdayReward = await db.birthdayReward.delete({
      where: {
        id: Number(id),
      },
      select: {
        id: true,
        birthdayRewardType: true,
        title: true,
        expiryMonths: true,
        rewardValue: true,
      },
    });
    return birthdayReward;
  }

  else {
    throw responseBadRequest("Invalid request method");
  }
}

export default function StoreCreditRewardBirthday() {
  const data = useLoaderData<typeof loader>();
  const [searchParams] = useSearchParams();
  const fetcher = useFetcher();

  const [title, setTitle] = useState(data.title);
  const [storeCredit, setStoreCredit] = useState(String(data.rewardValue));
  const [hasDifferentTiers, setHasDifferentTiers] = useState(false);
  const [isExpiryEnabled, setIsExpiryEnabled] = useState(!!data.expiryMonths);
  const [expiryMonths, setExpiryMonths] = useState(String(data.expiryMonths ?? 0));

  const navigate = useNavigate();
  const settingsId = searchParams.get('settingsId');

  const handleSaveReward = useCallback(async () => {
    // Update existing reward
    fetcher.submit(
      JSON.stringify({
        settingsId: settingsId,
        title: title,
        rewardValue: storeCredit,
        expiryMonths: isExpiryEnabled ? expiryMonths : undefined,
      }),
      {
        method: data.id ? "PATCH" : "POST",
        encType: "application/json",
      }
    );
    navigate(`/app/loyalties/birthday/`);
  }, [data.id, fetcher, settingsId, title, storeCredit, isExpiryEnabled, expiryMonths, navigate]);

  const handleDeleteReward = useCallback(async () => {
    // Delete existing reward
    await fetcher.submit(
      {},
      {
        method: "DELETE",
        encType: "application/json",
      }
    );
    navigate(`/app/loyalties/birthday/`);
  }, [fetcher, navigate]);

  return (
    <Page title="Store credit reward">
      <Layout>
        <InlineGrid columns={["twoThirds","oneThird"]}>
          <Layout.Section>
            <div style={{ marginTop: "1rem" }}>
              <Card>
                <TextField
                  label={
                    <Text as="h2" variant="headingMd">
                      Title
                    </Text>
                  }
                  value={title}
                  onChange={setTitle}
                  autoComplete="off"
                  helpText="Customers will see this in their cart and at checkout."
                />
              </Card>
            </div>

            <div style={{ marginTop: "1rem" }}>
              <Card>
                <Text as="h2" variant="headingMd">
                  Reward value
                </Text>
                <div style={{ marginTop: "0.5rem" }}>
                  <Checkbox
                    label="Different store credit values based on VIP tiers"
                    checked={hasDifferentTiers}
                    onChange={setHasDifferentTiers}
                  />
                </div>
                <div style={{ marginTop: "1rem" }}>
                  <TextField
                    label="Store credits"
                    value={storeCredit}
                    onChange={setStoreCredit}
                    autoComplete="off"
                    type="number"
                  />
                </div>
                <div style={{ marginTop: "1rem" }}>
                  <Button>Edit value</Button>
                </div>
              </Card>
            </div>
          </Layout.Section>

          <Layout.Section>
            <div style={{ marginTop: "1rem" }}>
              <Card>
                <Text as="h2" variant="headingMd">
                  Summary
                </Text>
                <div style={{ marginTop: "0.5rem" }}>
                  <List>
                    <List.Item>Store credit</List.Item>
                    <List.Item>Applies to all order</List.Item>
                  </List>
                </div>
              </Card>
            </div>

            <div style={{ marginTop: "1rem" }}>
              <Card>
                <InlineStack align="space-between" blockAlign="center">
                  <Text as="h2" variant="headingMd">Reward Expiry</Text>
                  <Button
                    pressed={isExpiryEnabled}
                    onClick={() => setIsExpiryEnabled(!isExpiryEnabled)}
                   tone={isExpiryEnabled ? "critical" : "success"}
                  >
                    {isExpiryEnabled ? 'Enabled' : 'Disabled'}
                  </Button>
                </InlineStack>
                {isExpiryEnabled ? (
                  <TextField
                    label="Expiry months"
                    value={expiryMonths}
                    onChange={setExpiryMonths}
                    type="number"
                    suffix="month(s)"
                    autoComplete="off"
                  />
                ) : (
                  <Text tone="subdued" as="p">
                    No expiry now. Turn on the switch to set expiry date.
                  </Text>
                )}
              </Card>
            </div>
          </Layout.Section>
        </InlineGrid>

        <Layout.Section>
          <InlineStack gap="600" align="space-between">
            { data.id ?
              <Button size="large" variant="primary" tone="critical" onClick={handleDeleteReward}>
                Delete
              </Button>
            : <BlockStack /> }
            <Button size="large" variant="primary" onClick={handleSaveReward}>
              Save
            </Button>
          </InlineStack>
        </Layout.Section>
      </Layout>
    </Page>
  );
}