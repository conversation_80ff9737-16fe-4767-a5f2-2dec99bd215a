/* eslint-disable @typescript-eslint/no-explicit-any */
import db from "@/db.server";
import { authenticate } from "@/shopify.server";
import findShop from "@/utils/find-shop.server";
import { LoyaltyProgramType } from "@prisma/client";
import { ActionFunctionArgs } from "@remix-run/node";
import { createPointsDiscount } from "./services";
import { ActionResponse } from "./types";

/**
 * Action function for the loyalty points page
 */
export async function action({ request }: ActionFunctionArgs): Promise<ActionResponse> {
  const { session, admin } = await authenticate.admin(request);
  const formData = await request.formData();
  const tabType = formData.get("tabType") as string;

  const shop = await findShop(admin, session);

  // Find or create loyalty program
  let loyaltyProgram = await db.loyaltyProgram.findFirst({
    where: {
      shopId: shop.id,
      programType: LoyaltyProgramType.POINTS,
    },
    include: {
      points: true,
    },
  });

  // Create new loyalty program if it doesn't exist
  loyaltyProgram ??= await db.loyaltyProgram.create({
    data: {
      shopId: shop.id,
      programType: LoyaltyProgramType.POINTS,
      isActive: true,
    },
    include: {
      points: true,
    },
  });

  // Handle form data based on which tab submitted the form
  if (tabType === "order") {
    // Get order tab form data
    const includeProductTotal = formData.get("includeProductTotal") === "true";
    const includeShipping = formData.get("includeShipping") === "true";
    const includeTaxes = formData.get("includeTaxes") === "true";
    const pointsIssueType = formData.get("pointsIssueType") as string;
    const issueDays = parseInt((formData.get("issueDays") as string) || "14");
    const orderStatus = formData.get("orderStatus") as string;
    const orderRefundType = formData.get("orderRefundType") as string;
    const redeemedRefundType = formData.get("redeemedRefundType") as string;

    // Create or update loyalty points with order settings
    if (loyaltyProgram.points) {
      // Update existing loyalty points with order settings
      await db.loyaltyPoints.update({
        where: {
          id: loyaltyProgram.points.id,
        },
        data: {
          includeProductTotal,
          includeShipping,
          includeTaxes,
          pointsIssueType: pointsIssueType.toUpperCase() as unknown as any,
          issueDays,
          orderStatus: orderStatus.toUpperCase().replace("-", "_") as unknown as any,
          orderRefundType: orderRefundType.toUpperCase() as unknown as any,
          redeemedRefundType: redeemedRefundType.toUpperCase() as unknown as any,
        },
      });
    } else {
      // Create new loyalty points with order settings
      await db.loyaltyPoints.create({
        data: {
          loyaltyProgramId: loyaltyProgram.id,
          programName: "Points Program",
          pointSingular: "Point",
          pointPlural: "Points",
          pointsPerCurrency: 1.0,
          currencyAmount: 1.0,
          pointsRedemptionValue: 0.01,
          maxRedeemPercentage: 30,
          includeProductTotal,
          includeShipping,
          includeTaxes,
          pointsIssueType: pointsIssueType.toUpperCase() as unknown as any,
          issueDays,
          orderStatus: orderStatus.toUpperCase() as unknown as any,
          orderRefundType: orderRefundType.toUpperCase() as unknown as any,
          redeemedRefundType: redeemedRefundType.toUpperCase() as unknown as any,
        },
      });
    }
  } else {
    // Handle points tab form data (existing logic)
    const programName = formData.get("programName") as string;
    const pointSingular = formData.get("pointSingular") as string;
    const pointPlural = formData.get("pointPlural") as string;
    const pointsPerCurrency = parseFloat((formData.get("pointsPerCurrency") as string) || "1.0");
    const currencyAmount = parseFloat((formData.get("currencyAmount") as string) || "1.0");
    const pointsRedemptionValue = parseFloat(
      (formData.get("pointsRedemptionValue") as string) || "0.01",
    );
    const pointsRedemptionAmount = parseFloat(
      (formData.get("pointsRedemptionAmount") as string) || "100",
    );
    const maxRedeemPercentage = parseInt((formData.get("maxRedeemPercentage") as string) || "30");
    const minPurchaseAmount = formData.get("minPurchaseAmount")
      ? parseFloat(formData.get("minPurchaseAmount") as string)
      : null;
    const roundingMethod = (formData.get("roundingMethod") as string) || "ROUND";
    const isConvertPointsByPercentage = formData.get("isConvertPointsByPercentage") === "true";

    // Create or update loyalty points with points tab settings
    if (loyaltyProgram.points) {
      // Update existing loyalty points
      await db.loyaltyPoints.update({
        where: {
          id: loyaltyProgram.points.id,
        },
        data: {
          programName,
          pointSingular,
          pointPlural,
          pointsPerCurrency,
          currencyAmount,
          pointsRedemptionValue,
          pointsRedemptionAmount,
          maxRedeemPercentage,
          minPurchaseAmount,
          roundingMethod,
          isConvertPointsByPercentage,
        },
      });
    } else {
      // Create new loyalty points
      await db.loyaltyPoints.create({
        data: {
          loyaltyProgramId: loyaltyProgram.id,
          programName,
          pointSingular,
          pointPlural,
          pointsPerCurrency,
          currencyAmount,
          pointsRedemptionValue,
          pointsRedemptionAmount,
          maxRedeemPercentage,
          minPurchaseAmount,
          roundingMethod,
          isConvertPointsByPercentage,
        },
      });
    }
    await createPointsDiscount(admin);
  }

  return { success: true };
}
