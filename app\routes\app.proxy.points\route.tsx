import db from "@/db.server";
import { authenticate } from "@/shopify.server";
import findShop from "@/utils/find-shop.server";
import { LoyaltyProgramType } from "@prisma/client";
import { LoaderFunctionArgs } from "@remix-run/node";

export async function loader({ request }: LoaderFunctionArgs) {
  if (request.method === "OPTIONS") {
    return new Response(null, {
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "*",
        "Access-Control-Allow-Headers": "Content-Type,Authorization",
      },
    });
  }
  const { session, admin } = await authenticate.public.appProxy(request);
  if (!session) {
    return Response.json({ error: "Unauthorized" }, { status: 401 });
  }

  const shop = await findShop(admin, session);

  console.log(session);

  const loyaltyProgram = await db.loyaltyProgram.findFirst({
    where: {
      shopId: shop.id,
      programType: LoyaltyProgramType.POINTS,
      isActive: true,
    },
    select: {
      id: true,
      programType: true,
      points: {
        select: {
          id: true,
          programName: true,
          pointPlural: true,
          pointSingular: true,
          pointsRedemptionAmount: true,
          pointsRedemptionValue: true,
          maxRedeemPercentage: true,
          minPurchaseAmount: true,
        },
      },
    },
  });

  if (!loyaltyProgram) {
    return Response.json({ success: false, error: "Loyalty program not found" }, { status: 404 });
  }

  return Response.json({ success: true, loyaltyProgram });
}

export async function action({ request }: LoaderFunctionArgs) {
  if (request.method === "OPTIONS") {
    return new Response(null, {
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "*",
        "Access-Control-Allow-Headers": "Content-Type,Authorization",
      },
    });
  }
  // const { sessionToken, cors } = await authenticate.public.checkout(request);

  return new Response(JSON.stringify({ my: "data", shop: "sessionToken.dest" }), {
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "*",
      "Access-Control-Allow-Headers": "Content-Type,Authorization",
    },
  });
}
