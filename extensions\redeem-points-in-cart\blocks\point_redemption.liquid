<!-- 1) UI template for the Web Component -->
<template id="point-redemption-template">
  <div class="point-redemption">
    <h3>Redeem Your Points</h3>
    <p>Your balance: <span id="pr-balance">0</span> points</p>
    <p>Conversion rate: <span id="pr-rate"></span></p>
    <label>
      <input type="checkbox" id="pr-use-points">
      Use <span id="pr-to-use">0</span> points
    </label>
  </div>
</template>

<script async src="{{ 'cart-listener.js' | asset_url }}"></script>
<!-- 2) Pass your editor settings into JS via JSON -->
<script id="point-redemption-config" type="application/json">
  {
    "conversionRate": {{ block.settings.point_conversion_rate | json }},
    "allowFullRedemption": {{ block.settings.allow_full_redemption | json }},
    "balance": {{ customer.metafields["$app"].points | json }},
    "customerId": {{ customer.id | json }}
  }
</script>

{% schema %}
{
  "name": "Point Redemption",
  "target": "body",
  "javascript": "point-redemption.js",
  "stylesheet": "point-redemption.css",
  "settings": [
    {
      "type": "number",
      "id": "point_conversion_rate",
      "label": "Point Conversion Rate",
      "default": 100
    },
    {
      "type": "checkbox",
      "id": "allow_full_redemption",
      "label": "Allow Full Redemption",
      "default": true
    },
    {
      "type": "text",
      "id": "button_text",
      "label": "Button text",
      "default": "Login"
    },
    {
      "type": "select",
      "id": "button_style",
      "label": "Button style",
      "options": [
        { "value": "primary", "label": "Primary (blue)" },
        { "value": "secondary", "label": "Secondary (outline)" }
      ],
      "default": "primary"
    }
  ],
  "enabled_on": {
    "templates": ["cart"]
  }
}
{% endschema %}
