import {LoyaltyProgramType} from "@prisma/client";
import type {LoaderFunctionArgs} from "@remix-run/node";
import db from "../../db.server";
import {authenticate} from "../../shopify.server";
import findShop from "../../utils/find-shop.server";
import {
  DEFAULT_ENTRY_METHOD,
  DEFAULT_EXPIRATION_DAYS,
  DEFAULT_EXPIRATION_TYPE,
  DEFAULT_ORDERS_COUNT,
  DEFAULT_VALIDATION_DAYS,
  DEFAULT_VALIDATION_TYPE,
} from "./constants";
import {WaysEarnRewardType} from "./types";

/**
 * Loader function for the signup program page
 */
export async function loader({request}: LoaderFunctionArgs) {
  const {admin, session} = await authenticate.admin(request);

  const shop = await findShop(admin, session);

  const waysEarnReward = await db.waysEarnReward.findFirst({
    where: {
      shopId: shop?.id,
      typeEarnReward: {
        in: [WaysEarnRewardType.PURCHASE],
      },
    },
    include: {
      purchaseRewards: true,
    },
  });

  // Find the VIP loyalty program
  let loyaltyProgram = await db.loyaltyProgram.findFirst({
    where: {
      shopId: shop.id,
      programType: LoyaltyProgramType.VIP_TIER,
    },
    include: {
      vipSettings: true,
    },
  });

  // Create the VIP loyalty program if it doesn't exist
  if (!loyaltyProgram) {
    loyaltyProgram = await db.loyaltyProgram.create({
      data: {
        shopId: shop.id,
        programType: LoyaltyProgramType.VIP_TIER,
        isActive: true,
        vipSettings: {
          create: {
            // Default settings
            entryMethod: DEFAULT_ENTRY_METHOD,
            ordersCount: DEFAULT_ORDERS_COUNT,
            validationType: DEFAULT_VALIDATION_TYPE,
            validationDays: DEFAULT_VALIDATION_DAYS,
            expirationType: DEFAULT_EXPIRATION_TYPE,
            expirationDays: DEFAULT_EXPIRATION_DAYS,
          },
        },
      },
      include: {
        vipSettings: true,
      },
    });
  } else if (!loyaltyProgram.vipSettings) {
    // Create VIP settings if they don't exist
    await db.loyaltyVIPSettings.create({
      data: {
        loyaltyProgramId: loyaltyProgram.id,
        // Default settings
        entryMethod: DEFAULT_ENTRY_METHOD,
        ordersCount: DEFAULT_ORDERS_COUNT,
        validationType: DEFAULT_VALIDATION_TYPE,
        validationDays: DEFAULT_VALIDATION_DAYS,
        expirationType: DEFAULT_EXPIRATION_TYPE,
        expirationDays: DEFAULT_EXPIRATION_DAYS,
      },
    });

    // Reload the loyalty program with the new settings
    loyaltyProgram = await db.loyaltyProgram.findFirst({
      where: {
        id: loyaltyProgram.id,
      },
      include: {
        vipSettings: true,
      },
    });
  }

  // Fetch VIP tiers if the program exists
  const vipTiers = await db.loyaltyVIPTier.findMany({
    where: {
      loyaltyProgramId: loyaltyProgram?.id,
    },
    orderBy: {
      createdAt: "asc",
    }
  });

  const loyaltyProgramPoint = await db.loyaltyProgram.findFirst({
    where: {
      shopId: shop?.id,
      programType: LoyaltyProgramType.POINTS,
    },
    include: {
      points: true,
    },
  });

  // Fetch Points Settings if the program exists
  const pointsSettings = await db.loyaltyPoints.findMany({
    where: {
      loyaltyProgramId: loyaltyProgramPoint?.id,
    },
  });

  return Response.json({
    waysEarnReward,
    vipTiers,
    pointsSettings,
  });
}
