# Shopify OMO Integration - README

## Overview
Shopify OMO Integration is designed to unify online and in-store transactions by integrating Shopify with physical store operations. The project aims to provide seamless order processing, shipping, customer service, and member order behavior analysis while maintaining a consistent membership system across all channels.

### Objectives
- Enable Shopify to integrate with in-store POS systems.
- Synchronize customer data between Shopify and POS providers.
- Manage loyalty programs and discounts for both online and offline sales.
- Provide RESTful API endpoints for service providers to access Shopify store data.
- Ensure compatibility with Shopify Basic, Advanced, and Plus plans.

### Target Users
Retailers using Shopify who operate or plan to operate physical stores requiring POS integration for unified commerce operations.

## Features
- **Unified Membership System**: Accumulate spending conditions and reward benefits across online and offline stores.
- **Member Management**: Add, query, modify, and deactivate memberships.
- **Discount & Offer Management**: Fetch and redeem member offers and discounts.
- **Order Synchronization**: Sync in-store purchases with Shopify for consolidated reporting.
- **Staff & Store Management**: Manage store locations and staff accounts linked to Shopify.

## API Endpoints
The Shopify OMO Integration provides **7 RESTful API endpoints** to facilitate POS providers in accessing and managing Shopify store data.

### 1. Add New Member
- **Method**: `POST`
- **Endpoint**: `/api/members`
- **Description**: Registers a new in-store customer as a Shopify member.
- **Required Fields**: `cellphone`, `firstName`, `lastName`, `email`, `birthday`, `gender`, `registerDate`, `registerLocation`, `registerEmployee`

### 2. Query Member Basic Information
- **Method**: `GET`
- **Endpoint**: `/api/members`
- **Description**: Retrieves basic membership details.
- **Required Fields**: `cellphone` or `memberId`

### 3. Modify Member Basic Information
- **Method**: `PATCH`
- **Endpoint**: `/api/members/{memberId}`
- **Description**: Updates member profile details.
- **Required Fields**: `cellphone`, `email`, `address`, `marketingNotify`

### 4. Cancel Membership
- **Method**: `POST`
- **Endpoint**: `/api/members/cancel`
- **Description**: Deactivates a member’s account.
- **Required Fields**: `cellphone`, `memberId`, `cancelDate`

### 5. Query Member's Available Offers
- **Method**: `GET`
- **Endpoint**: `/api/offers/availables`
- **Description**: Retrieves available discount codes and loyalty points for a member.
- **Required Fields**: `cellphone`, `memberId`

### 6. Redeem Member Offers
- **Method**: `POST`
- **Endpoint**: `/api/offers/redeem`
- **Description**: Applies discounts, promotional gifts, or loyalty points.
- **Required Fields**: `cellphone`, `memberId`, `orderId`, `point`, `couponCodes`

### 7. Add In-Store Member Order
- **Method**: `POST`
- **Endpoint**: `/api/orders`
- **Description**: Syncs in-store transactions with Shopify.
- **Required Fields**: `cellphone`, `memberId`, `purchaseType`, `orderId`, `orderDate`, `orderAmount`, `productName`, `productSku`, `staffId`, `locationId`

## Authentication
- API requests require **Bearer Token Authentication**.
- Each customer/vendor will have a **unique API key** to access store data.
- API logs track store and staff activity to ensure data security.

## Deployment & Scalability
- Supports Shopify Basic, Advanced, and Plus plans.
- Future updates will allow paid subscription-based models.
- Compatible with Shopify POS and Shopify Flow.

## Installation
### Prerequisites
Before you begin, you'll need the following:
1. **Node.js**: [Download and install](https://nodejs.org/en/download/) it if you haven't already.
2. **Shopify Partner Account**: [Create an account](https://partners.shopify.com/signup) if you don't have one.
3. **Test Store**: Set up either a [development store](https://help.shopify.com/en/partners/dashboard/development-stores#create-a-development-store) or a [Shopify Plus sandbox store](https://help.shopify.com/en/partners/dashboard/managing-stores/plus-sandbox-store) for testing your app.

### Setup
Using yarn:
```shell
yarn install
```
Using npm:
```shell
npm install
```
Using pnpm:
```shell
pnpm install
```

## Running the Project
Using yarn:
```shell
yarn dev
```
Using npm:
```shell
npm run dev
```
Using pnpm:
```shell
pnpm run dev
```
Press `P` to open the URL to your app. Once you click install, you can start development.

## Additional Resources
- [Shopify API Documentation](https://shopify.dev/docs/api/shopify-app-remix)
- [Remix Framework](https://remix.run)
- [Shopify App Bridge](https://shopify.dev/docs/api/app-bridge)

For further details, refer to the [API Documentation](./SHO-API-Doc-Reference.pdf).

