import { gql } from "@apollo/client/core";

interface Money {
  amount: string | number;
  currencyCode: string;
}

interface MetafieldNode {
  key: string;
  type: string;
  value: string;
}

export const createDraftOrderMutation = gql`
  mutation draftOrderCreate($input: DraftOrderInput!) {
    draftOrderCreate(input: $input) {
      userErrors {
        field
        message
      }
      draftOrder {
        id
        customer {
          id
          email
          phone
        }
        discountCodes
        lineItems(first: 100) {
          nodes {
            id
            variant {
              id
            }
            sku
            title
            quantity
            originalUnitPriceSet {
              shopMoney {
                amount
                currencyCode
              }
            }
            totalDiscountSet {
              shopMoney {
                amount
                currencyCode
              }
            }
            discountedTotalSet {
              shopMoney {
                amount
                currencyCode
              }
            }
          }
        }
        totalLineItemsPriceSet {
          shopMoney {
            amount
            currencyCode
          }
        }
        totalDiscountsSet {
          shopMoney {
            amount
            currencyCode
          }
        }
        totalPriceSet {
          shopMoney {
            amount
            currencyCode
          }
        }
        metafields(first: 10) {
          nodes {
            namespace
            key
            type
            value
          }
        }
      }
    }
  }
`;

export interface CreateDraftOrderMutationInput {
  input: {
    purchasingEntity?: {
      customerId: string;
    };
    taxExempt: boolean;
    lineItems: {
      title?: string;
      sku: string;
      variantId?: string;
      originalUnitPriceWithCurrency?: Money;
      priceOverride?: Money;
      quantity: number;
    }[];
    discountCodes?: string[];
    metafields: MetafieldNode[];
  };
}

export interface CreateDraftOrderMutationResponse {
  draftOrderCreate: {
    userErrors: {
      field: string[];
      message: string;
    }[];
    draftOrder: {
      id: string;
      customer?: {
        id: string;
        email: string;
        phone: string;
      };
      discountCodes: string[];
      lineItems: {
        nodes: {
          id: string;
          variant: {
            id: string;
          };
          sku: string;
          title: string;
          quantity: number;
          originalUnitPriceSet: {
            shopMoney: Money;
          };
          totalDiscountSet: {
            shopMoney: Money;
          };
          discountedTotalSet: {
            shopMoney: Money;
          };
          taxLines: {
            title: string;
            rate: number;
            priceSet: {
              shopMoney: Money;
            };
          }[];
        }[];
      };
      totalLineItemsPriceSet: {
        shopMoney: Money;
      };
      totalDiscountsSet: {
        shopMoney: Money;
      };
      totalPriceSet: {
        shopMoney: Money;
      };
      metafields: {
        nodes: MetafieldNode[];
      };
    };
  };
}

export const draftOrderCompleteMutation = gql`
  mutation draftOrderComplete($id: ID!) {
    draftOrderComplete(id: $id) {
      draftOrder {
        id
        metafield(key: "order_id") {
          value
        }
        order {
          id
          lineItems(first: 100) {
            nodes {
              id
              sku
            }
          }
        }
      }
    }
}
`

export interface DraftOrderCompleteMutationInput {
  id: string;
}

export interface DraftOrderCompleteMutationResponse {
  draftOrderComplete: {
    userErrors: {
      field: string[];
      message: string;
    }[]
    draftOrder: {
      id: string;
      metafield: {
        value: string;
      }
      order: {
        id: string;
        lineItems: {
          nodes: {
            id: string;
            sku: string;
          }[];
        };
        // metafields: {
        //   nodes: MetafieldNode[]
        // }
      };
    }
  }
}

export const refundCreateMutation = gql`
  mutation M($input: RefundInput!) {
    refundCreate(input: $input) {
      userErrors {
        field
        message
      }
      order {
        id
        metafields(first: 10) {
          nodes {
            namespace
            key
            type
            value
          }
        }
      }
      refund {
        id
        refundLineItems(first: 100) {
          nodes {
            id
            lineItem {
              id
              sku
              name
            }
            quantity
            priceSet {
              shopMoney {
                amount
                currencyCode
              }
            }
          }
        }
        totalRefundedSet {
          shopMoney {
            amount
            currencyCode
          }
        }
      }
    }
  }
`;

export interface RefundCreateMutationInput {
  input: {
    orderId: string;
    refundLineItems: {
      lineItemId: string;
      quantity: number;
    }[];
    transactions: {
      amount: number;
      gateway: string;
      kind: "REFUND";
      orderId: string;
      parentId?: string;
    }[];
  };
}

export interface RefundCreateMutationResponse {
  refundCreate: {
    userErrors: {
      field: string[];
      message: string;
    }[];
    order: {
      id: string;
      metafields: {
        nodes: MetafieldNode[];
      };
    };
    refund: {
      id: string;
      refundLineItems: {
        nodes: {
          id: string;
          lineItem: {
            id: string;
            name: string;
            sku: string;
          }
          quantity: number;
          priceSet: {
            shopMoney: Money;
          }
        }[];
      };
      totalRefundedSet: {
        shopMoney: Money;
      };
    };
  };
}

export const setMetafieldsMutation = gql`
  mutation MetafieldsSet($metafields: [MetafieldsSetInput!]!) {
    metafieldsSet(metafields: $metafields) {
      metafields {
        key
        namespace
        value
        createdAt
        updatedAt
      }
      userErrors {
        field
        message
        code
      }
    }
  }
`

export interface SetMetafieldsMutationInput {
  metafields: {
    key: string;
    namespace: string;
    ownerId: string;
    type: string;
    value: string;
  }[];
}
