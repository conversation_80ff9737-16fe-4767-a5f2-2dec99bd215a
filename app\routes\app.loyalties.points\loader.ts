import { LoyaltyProgramType } from "@prisma/client";
import { LoaderFunctionArgs } from "@remix-run/node";
import db from "../../db.server";
import { authenticate } from "../../shopify.server";
import { LoyaltyPointsData } from "./types";

/**
 * Loader function for the loyalty points page
 */
export async function loader({ request }: LoaderFunctionArgs) {
  const { session } = await authenticate.admin(request);

  // Get the shop information
  const shop = await db.shop.findFirst({
    where: {
      myshopifyDomain: session.shop,
    },
    include: {
      loyaltyPrograms: {
        where: {
          programType: LoyaltyProgramType.POINTS,
        },
        include: {
          points: true,
        },
      },
    },
  });

  if (!shop) {
    throw new Error(`Shop not found for domain ${session.shop}`);
  }

  const loyaltyProgram = shop.loyaltyPrograms[0];
  const loyaltyPoints = loyaltyProgram?.points;

  return { loyaltyPoints };
}

/**
 * Type for the loader data
 */
export type LoaderData = {
  loyaltyPoints: LoyaltyPointsData | null;
};
