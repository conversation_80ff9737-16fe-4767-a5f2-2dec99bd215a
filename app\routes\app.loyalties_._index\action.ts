import { LoyaltyProgramType } from "@prisma/client";
import { ActionFunctionArgs, redirect } from "@remix-run/node";
import { authenticate } from "app/shopify.server";
import db from "../../db.server";

/**
 * Action function for the loyalties index page
 * Handles form submissions for loyalty programs and ways to earn rewards
 */
export async function action({ request }: ActionFunctionArgs) {
  const { session } = await authenticate.admin(request);
  const formData = await request.formData();

  if (formData.has("toggleId")) {
    const id = Number(formData.get("toggleId"));
    const state = formData.get("toggleState") === "1";

    // update in DB and return the updated row to the fetcher
    await db.waysEarnReward.update({
      where: { id },
      data: { isActive: state },
      include: { DefaultWaysEarnRewardType: true },
    });
    return redirect(".");
  }

  // Handle adding ways to earn rewards
  if (formData.has("typeIds")) {
    const shop = await db.shop.findFirst({
      where: { myshopifyDomain: session.shop },
    });
    if (!shop) throw new Error("Shop not found");

    const typeIds = formData.getAll("typeIds").map((v) => Number(v));

    for (const defaultTypeId of typeIds) {
      const def = await db.defaultWaysEarnRewardType.findUnique({
        where: { id: defaultTypeId },
      });
      if (!def) continue;

      await db.waysEarnReward.upsert({
        where: {
          shopId_defaultWaysEarnRewardTypeId: {
            shopId: shop.id,
            defaultWaysEarnRewardTypeId: defaultTypeId,
          },
        },
        update: { isActive: true },
        create: {
          shopId: shop.id,
          defaultWaysEarnRewardTypeId: defaultTypeId,
          isActive: true,
          // fill in the enum & title so Prisma is happy:
          typeEarnReward: def.code,
          title: "",
          subtitle: "",
        },
      });
    }

    // Reload the same page so loader can fetch the new saved items
    return redirect(".");
  }

  // Handle toggling loyalty program active status
  const isActive = formData.get("isActive") === "true";
  const programType = formData.get("programType") as keyof typeof LoyaltyProgramType;

  const shop = await db.shop.findFirst({
    where: { myshopifyDomain: session.shop },
  });
  if (!shop) throw new Error("Shop not found");

  const existing = await db.loyaltyProgram.findFirst({
    where: { shopId: shop.id, programType },
  });
  if (existing) {
    await db.loyaltyProgram.update({
      where: { id: existing.id },
      data: { isActive },
    });
  } else {
    await db.loyaltyProgram.create({
      data: { shopId: shop.id, programType, isActive },
    });
  }
  return redirect(".");
}
