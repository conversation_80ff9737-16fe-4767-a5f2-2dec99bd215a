import {<PERSON>, useF<PERSON>cher, useLoaderData, useNavigate} from "@remix-run/react";
import {useAppBridge} from "@shopify/app-bridge-react";
import {BlockStack, Box, Button, Card, InlineStack, Text} from "@shopify/polaris";
import {PlusIcon} from "@shopify/polaris-icons";
import {useCallback, useState} from "react";
import {useTranslation} from "react-i18next";
import {DELETE_VIP_TIER, MODAL_IDS} from "../constants";
import {ActionResponse, LoaderData} from "../types";
import DeleteVIPTierConfirmationModal from "./DeleteVIPTierConfirmationModal";
import TierCard from "./TierCard";

/**
 * A component that displays the list of VIP tiers
 */
export default function VIPTiersList() {
  // Get vipTiers directly from loader data
  const { vipTiers } = useLoaderData<LoaderData>();
  const shopify = useAppBridge();
  const fetcher = useFetcher<ActionResponse>();
  const { t } = useTranslation();

  // State for tier to delete
  const [tierToDelete, setTierToDelete] = useState<{ id: number; name: string } | null>(null);

  // Helper function to get icon tone based on tier index
  const getIconToneForIndex = useCallback(
    (index: number): "info" | "base" | "warning" | "success" | "critical" => {
      const tones: Array<"info" | "base" | "warning" | "success" | "critical"> = [
        "info",
        "base",
        "warning",
        "success",
        "critical",
      ];
      return tones[index % tones.length];
    },
    [],
  );

  // Handler for editing a tier
  const navigate = useNavigate();

  const handleEditTier = useCallback(
    (tierId: number) => {
      // Navigate to edit page
      navigate(`/app/loyalties/vip/edit/${tierId}`);
    },
    [navigate],
  );

  // Handler for deleting a tier
  const handleDeleteTier = useCallback(
    (tierId: number) => {
      // Find the tier to delete
      const tier = vipTiers.find((t) => t.id === tierId);
      if (tier) {
        setTierToDelete({ id: tierId, name: tier.name });
        shopify?.modal?.show(MODAL_IDS.DELETE_VIP_TIER_CONFIRMATION);
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [vipTiers],
  );

  // Handler for confirming tier deletion
  const handleConfirmDeleteTier = useCallback(
    (tierId?: number) => {
      if (tierId) {
        // Submit the delete action
        const jsonData = {
          actionType: DELETE_VIP_TIER,
          tierId,
        };

        fetcher.submit(JSON.stringify(jsonData), { method: "post", encType: "application/json" });

        // Show a toast notification
        shopify?.toast.show(t("loyalties.vip.tiers.deletingMessage"), {
          isError: false,
          duration: 3000,
        });

        // Hide the modal
        shopify?.modal?.hide(MODAL_IDS.DELETE_VIP_TIER_CONFIRMATION);
        setTierToDelete(null);
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [fetcher],
  );

  // Handler for canceling tier deletion
  const handleCancelDeleteTier = useCallback(() => {
    shopify?.modal?.hide(MODAL_IDS.DELETE_VIP_TIER_CONFIRMATION);
    setTierToDelete(null);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <BlockStack gap="200">
      <Text variant="headingLg" as="h5">
        {t("loyalties.programTypes.vip")}
      </Text>

      <Card roundedAbove="xs">
        <BlockStack gap="400">
          <InlineStack align="space-between">
            <BlockStack gap="100">
              <Text as="h3" variant="headingMd">
                {t("loyalties.vip.tiers.title")}
              </Text>
              <Text as="p" variant="bodyMd" tone="subdued">
                {t("loyalties.vip.tiers.description")}
              </Text>
            </BlockStack>
            <Link to="/app/loyalties/vip/new">
              <Button icon={PlusIcon}>{t("loyalties.vip.tiers.addNew")}</Button>
            </Link>
          </InlineStack>

          <Box borderColor="border" borderRadius="100">
            {vipTiers.length > 0 &&
              vipTiers.map((tier, index) => (
                <TierCard
                  key={tier.id}
                  tierName={tier.name}
                  iconTone={getIconToneForIndex(index)}
                  spentAmount={tier.spendRequirement ? tier.spendRequirement.toString() : "0"}
                  showDetails={true}
                  onEdit={() => handleEditTier(tier.id)}
                  onDelete={() => handleDeleteTier(tier.id)}
                />
              ))}
          </Box>
        </BlockStack>
      </Card>

      {/* Delete Confirmation Modal */}
      <DeleteVIPTierConfirmationModal
        tierId={tierToDelete?.id}
        tierName={tierToDelete?.name}
        onConfirm={handleConfirmDeleteTier}
        onCancel={handleCancelDeleteTier}
      />
    </BlockStack>
  );
}
