import type { LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData, useNavigate } from "@remix-run/react";
import { useAppBridge } from "@shopify/app-bridge-react";
import {
  BlockStack,
  Box,
  Button,
  Card,
  InlineGrid,
  InlineStack,
  Layout,
  Text,
  TextField,
} from "@shopify/polaris";
import { useCallback, useState } from "react";

import { useTranslation } from "react-i18next";
import db from "../../db.server";
import { authenticate } from "../../shopify.server";
import responseBadRequest from "../../utils/response.badRequest";
import StoreCreditEditModal from "./edit_modal";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { admin } = await authenticate.admin(request);

  const response = await admin.graphql(`
    #graphql
      query GetShopDetails {
        shop {
          id
          name
          myshopifyDomain
        }
      }
    `);

  const responseJson = await response.json();
  const { id: localShopId } = await db.shop.findFirstOrThrow({
    where: {
      shopId: responseJson.data.shop.id,
    },
    select: {
      id: true,
      shopId: true,
    },
  });

  const creditLoyaltyProgram = await db.shopCreditLoyaltyProgram.upsert({
    where: {
      shopId: localShopId,
    },
    create: {
      shopId: localShopId,
      singularName: "Store credit",
    },
    update: {},
    select: {
      id: true,
      isActive: true,
      singularName: true,
      pluralName: true,
    },
  });

  return creditLoyaltyProgram;
};

export const action = async ({ request }: LoaderFunctionArgs) => {
  if (request.method === "POST") {
    const { id, singularName, pluralName } = await request.json();

    if (singularName) {
      return await db.shopCreditLoyaltyProgram.update({
        where: {
          id: Number(id),
        },
        data: {
          singularName,
          pluralName,
        },
        select: {
          id: true,
          singularName: true,
          pluralName: true,
        },
      });
    } else {
      return null;
    }
  }
  throw responseBadRequest([], "Method not allowed");
};

export default function ProgramStoreCreditPage() {
  const data = useLoaderData<typeof loader>();
  const { t } = useTranslation();

  const [singularTerm, setSingularTerm] = useState(data.singularName);
  const [pluralTerm, setPluralTerm] = useState(data.pluralName ?? "");
  const [rewardRate, setRewardRate] = useState("");

  const navigate = useNavigate();
  const shopify = useAppBridge();

  const handleEditModalOpen = useCallback(() => {
    shopify?.modal?.show("store-credit-edit-modal");
  }, [shopify]);

  const handleEditModalClose = useCallback(() => {
    shopify?.modal?.hide("store-credit-edit-modal");
  }, [shopify]);

  const handleSave = async () => {
    await fetch("/app/loyalties/store-credit", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        id: data.id,
        singularName: singularTerm,
        pluralName: pluralTerm,
        rewardRate,
      }),
    });
  };

  return (
    <Box>
      <Layout>
        <Layout.Section>
          <Card>
            {/* Store credit name */}
            <BlockStack gap="500">
              <InlineGrid gap="200" columns={2}>
                {/* Store credit name */}
                <BlockStack gap="200">
                  <Text variant="headingLg" as="h3" fontWeight="semibold">
                    {t("loyalties.storeCredit.name.title")}
                  </Text>
                  <Text variant="bodyMd" as="p" tone="subdued">
                    {t("loyalties.storeCredit.name.description")}
                  </Text>
                </BlockStack>

                <BlockStack gap="200">
                  <TextField
                    label=""
                    value={singularTerm}
                    onChange={setSingularTerm}
                    autoComplete="off"
                    helpText={t("loyalties.storeCredit.name.singularHelpText")}
                  />
                  <TextField
                    label=""
                    value={pluralTerm}
                    onChange={setPluralTerm}
                    autoComplete="off"
                    helpText={t("loyalties.storeCredit.name.pluralHelpText")}
                  />
                </BlockStack>

                {/* Store credit reward rate */}
                <BlockStack gap="200">
                  <Text variant="headingLg" as="h3" fontWeight="semibold">
                    {t("loyalties.storeCredit.rewardRate.title")}
                  </Text>
                  <Text variant="bodyMd" as="p" tone="subdued">
                    {t("loyalties.storeCredit.rewardRate.description")}
                  </Text>
                </BlockStack>
                <BlockStack align="center" inlineAlign="start" gap="200">
                  <Button onClick={handleEditModalOpen} textAlign="right">
                    {t("loyalties.storeCredit.rewardRate.editButton")}
                  </Button>
                </BlockStack>
              </InlineGrid>
            </BlockStack>

            <Box paddingBlockEnd="500" />

            {/* Store credit reward rate */}
            <BlockStack gap="400">
              <InlineStack gap="200" align="space-between">
                <StoreCreditEditModal
                  id="store-credit-edit-modal"
                  navigate={navigate}
                  onHide={handleEditModalClose}
                />
              </InlineStack>
            </BlockStack>
          </Card>
        </Layout.Section>

        <Layout.Section>
          <InlineStack align="center">
            <Button size="large" variant="primary" onClick={handleSave}>
              Save
            </Button>
          </InlineStack>
        </Layout.Section>
      </Layout>
    </Box>
  );
}
