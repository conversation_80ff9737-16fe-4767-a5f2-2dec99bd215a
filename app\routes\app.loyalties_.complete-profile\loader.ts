import type { LoaderFunctionArgs } from "@remix-run/node";
import db from "../../db.server";
import { authenticate } from "../../shopify.server";

/**
 * Loader function for the signup program page
 */
export async function loader({ request }: LoaderFunctionArgs) {
  const { session } = await authenticate.admin(request);

  const shop = await db.shop.findFirst({ where: { myshopifyDomain: session.shop } });
  if (!shop) {
    throw new Error("Shop not found");
  }

  let completeProfileSettings = await db.completeProfileSettings.findFirst({
    where: { shopId: shop.id },
    include: {
      rewards: true,
      customProfile: {
        include: {
          gender: true,
        },
      },
    },
  });

  if (!completeProfileSettings) {
    const customProfile = await db.customProfile.create({
      data: { editable: true },
    });
    await db.gender.createMany({
      data: [
        { name: "Male", customProfileId: customProfile.id },
        { name: "Female", customProfileId: customProfile.id },
        { name: "Transgender", customProfileId: customProfile.id },
        { name: "Non-binary", customProfileId: customProfile.id },
        { name: "Prefer not to say", customProfileId: customProfile.id },
      ],
    });
    completeProfileSettings = await db.completeProfileSettings.create({
      data: { shopId: shop.id, customProfileId: customProfile.id },
      include: {
        rewards: true,
        customProfile: {
          include: {
            gender: true,
          },
        },
      },
    });
  }

  const initialRewards = await db.completeProfileReward.findMany({
    where: { completeProfileSettingsId: completeProfileSettings?.id },
  });

  return Response.json({ initialRewards, completeProfileSettings });
}
