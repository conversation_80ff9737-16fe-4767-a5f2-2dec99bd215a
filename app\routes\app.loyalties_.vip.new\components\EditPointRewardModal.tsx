import { Modal, TitleBar } from "@shopify/app-bridge-react";
import { BlockStack, InlineStack, Text, TextField } from "@shopify/polaris";
import { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { MODAL_IDS, PointRewardInterface } from "../interface";

interface EditPointRewardModalProps {
  reward: PointRewardInterface;
  onSave?: (reward: PointRewardInterface) => void;
  onClose?: () => void;
}

export default function EditPointRewardModal({
  reward,
  onSave,
  onClose,
}: Readonly<EditPointRewardModalProps>) {
  const { t } = useTranslation();
  const [title, setTitle] = useState(reward.title);
  const [value, setValue] = useState(reward.value);

  // Update state when reward changes
  useEffect(() => {
    setTitle(reward.title);
    setValue(reward.value);
  }, [reward]);

  const handleSave = useCallback(() => {
    if (onSave) {
      const updatedReward: PointRewardInterface = {
        ...reward,
        title,
        value,
      };
      onSave(updatedReward);
    }
  }, [onSave, reward, title, value]);

  return (
    <Modal id={MODAL_IDS.EDIT_POINT_REWARD}>
      <div className="m-3">
        <BlockStack gap="400">
          <Text variant="headingMd" as="h2">
            {t("loyalties.rewards.editPointReward")}
          </Text>
          <BlockStack>
            <Text variant="bodyMd" as="p">
              {t("loyalties.rewards.rewardTitle")}
            </Text>

            <TextField
              label={t("loyalties.rewards.rewardTitle")}
              value={title}
              type="text"
              onChange={setTitle}
              autoComplete="off"
              labelHidden
            />
          </BlockStack>

          <BlockStack>
            <Text variant="bodyMd" as="p">
              {t("loyalties.rewards.rewardValue")}
            </Text>
            <InlineStack gap="200" align="start" blockAlign="center">
              <TextField
                label={t("loyalties.rewards.rewardValue")}
                type="integer"
                value={value}
                onChange={setValue}
                autoComplete="off"
                labelHidden
              />

              <Text variant="bodyMd" as="p">
                {t("loyalties.rewards.points")}
              </Text>
            </InlineStack>
          </BlockStack>
        </BlockStack>
      </div>
      <TitleBar title={t("loyalties.rewards.editReward")}>
        <button onClick={handleSave}>{t("common.save")}</button>
        <button onClick={onClose}>{t("common.cancel")}</button>
      </TitleBar>
    </Modal>
  );
}
