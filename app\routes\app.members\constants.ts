// File: app/routes/app.members/constants.ts
export const ITEMS_PER_PAGE = 25;

export const SEARCH_TYPE_OPTIONS = [
  { label: "Email", value: "email" },
  { label: "Name", value: "name" },
];

export const MEMBER_RESOURCE_NAME = {
  singular: "customer",
  plural: "customers",
};

export enum MemberVipTier {
  STARTER = "Starter",
  VIP = "VIP",
  SVIP = "SVIP",
  SSVIP = "SSVIP",
}

export const MEMBER_TABLE_HEADINGS: { title: string }[] = [
  { title: "Name" },
  { title: "Points" },
  { title: "VIP Tier" },
  { title: "Orders" },
  { title: "Total Spent" },
];
